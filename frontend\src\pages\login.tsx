import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Form, Input, Button, Card, Typography, message, Space, Divider } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { useAuthStore } from '@/store';
import { LoginRequest } from '@/types';

const { Title, Text } = Typography;

const LoginPage: React.FC = () => {
  const router = useRouter();
  const { login, isAuthenticated, isLoading } = useAuthStore();
  const [form] = Form.useForm();

  // 如果已经登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      router.replace('/');
    }
  }, [isAuthenticated, router]);

  const handleLogin = async (values: LoginRequest) => {
    try {
      await login(values);
      message.success('登录成功！');
      router.replace('/');
    } catch (error: any) {
      message.error(error.response?.data?.detail || '登录失败，请检查用户名和密码');
    }
  };

  const handleQuickLogin = (username: string, password: string) => {
    form.setFieldsValue({ username, password });
    handleLogin({ username, password });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo 和标题 */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-jiyang-500 rounded-full mb-4">
            <span className="text-white text-2xl font-bold">暨</span>
          </div>
          <Title level={2} className="text-gray-800 mb-2">
            暨阳湖大酒店
          </Title>
          <Text type="secondary" className="text-lg">
            传菜管理系统
          </Text>
        </div>

        {/* 登录表单 */}
        <Card className="shadow-lg border-0">
          <Form
            form={form}
            name="login"
            onFinish={handleLogin}
            autoComplete="off"
            size="large"
          >
            <Form.Item
              name="username"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3个字符' },
              ]}
            >
              <Input
                prefix={<UserOutlined className="text-gray-400" />}
                placeholder="用户名"
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined className="text-gray-400" />}
                placeholder="密码"
                autoComplete="current-password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={isLoading}
                icon={<LoginOutlined />}
                className="w-full h-12 text-lg font-medium bg-jiyang-500 hover:bg-jiyang-600 border-jiyang-500 hover:border-jiyang-600"
              >
                登录
              </Button>
            </Form.Item>
          </Form>

          <Divider>
            <Text type="secondary">快速登录</Text>
          </Divider>

          {/* 快速登录按钮 */}
          <Space direction="vertical" className="w-full" size="small">
            <Button
              type="default"
              size="small"
              className="w-full"
              onClick={() => handleQuickLogin('admin', 'admin123')}
              disabled={isLoading}
            >
              管理员登录 (admin/admin123)
            </Button>
            <Button
              type="default"
              size="small"
              className="w-full"
              onClick={() => handleQuickLogin('manager01', 'manager123')}
              disabled={isLoading}
            >
              经理登录 (manager01/manager123)
            </Button>
            <Button
              type="default"
              size="small"
              className="w-full"
              onClick={() => handleQuickLogin('waiter01', 'waiter123')}
              disabled={isLoading}
            >
              服务员登录 (waiter01/waiter123)
            </Button>
            <Button
              type="default"
              size="small"
              className="w-full"
              onClick={() => handleQuickLogin('chef01', 'chef123')}
              disabled={isLoading}
            >
              厨师长登录 (chef01/chef123)
            </Button>
          </Space>
        </Card>

        {/* 版权信息 */}
        <div className="text-center mt-8">
          <Text type="secondary" className="text-sm">
            © 2024 暨阳湖大酒店. 保留所有权利.
          </Text>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
