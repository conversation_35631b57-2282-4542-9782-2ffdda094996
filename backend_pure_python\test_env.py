#!/usr/bin/env python3
"""
测试Python环境
"""
import sys
print(f"Python版本: {sys.version}")
print(f"Python路径: {sys.executable}")

try:
    import fastapi
    print(f"✅ FastAPI版本: {fastapi.__version__}")
except ImportError as e:
    print(f"❌ FastAPI导入失败: {e}")

try:
    import uvicorn
    print(f"✅ Uvicorn已安装")
except ImportError as e:
    print(f"❌ Uvicorn导入失败: {e}")

try:
    import sqlalchemy
    print(f"✅ SQLAlchemy版本: {sqlalchemy.__version__}")
except ImportError as e:
    print(f"❌ SQLAlchemy导入失败: {e}")

print("环境检查完成")
