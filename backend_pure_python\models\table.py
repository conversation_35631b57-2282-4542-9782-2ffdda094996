from sqlalchemy import Column, Integer, String, Boolean, DateTime, Enum, Text, Numeric, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from core.database import Base


class TableType(str, enum.Enum):
    """餐桌类型枚举"""
    PRIVATE_ROOM = "private_room"  # 包厢
    HALL_TABLE = "hall_table"      # 大厅餐桌
    VIP_ROOM = "vip_room"          # VIP包厢
    OUTDOOR = "outdoor"            # 户外餐桌


class TableStatus(str, enum.Enum):
    """餐桌状态枚举"""
    AVAILABLE = "available"        # 空闲可用
    RESERVED = "reserved"          # 已预订
    OCCUPIED = "occupied"          # 使用中
    CLEANING = "cleaning"          # 清理中
    MAINTENANCE = "maintenance"    # 维护中
    OUT_OF_SERVICE = "out_of_service"  # 停用


class Table(Base):
    """餐桌/包厢模型"""
    __tablename__ = "tables"
    __table_args__ = (
        Index('idx_table_status', 'status'),
        Index('idx_table_active', 'is_active'),
        Index('idx_table_number', 'number'),
    )
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 基本信息
    number = Column(String(20), unique=True, index=True, nullable=False, comment="桌号/包厢号")
    name = Column(String(100), nullable=True, comment="餐桌/包厢名称")
    table_type = Column(Enum(TableType), nullable=False, default=TableType.HALL_TABLE, comment="类型")
    status = Column(Enum(TableStatus), nullable=False, default=TableStatus.AVAILABLE, comment="状态")
    
    # 容量信息
    capacity = Column(Integer, nullable=False, default=4, comment="容纳人数")
    min_capacity = Column(Integer, nullable=True, comment="最少用餐人数")
    
    # 位置信息
    floor = Column(String(20), nullable=True, comment="楼层")
    area = Column(String(50), nullable=True, comment="区域")
    location_description = Column(Text, nullable=True, comment="位置描述")
    
    # 设施信息
    has_tv = Column(Boolean, default=False, comment="是否有电视")
    has_karaoke = Column(Boolean, default=False, comment="是否有KTV")
    has_mahjong = Column(Boolean, default=False, comment="是否有麻将")
    has_projector = Column(Boolean, default=False, comment="是否有投影仪")
    has_wifi = Column(Boolean, default=True, comment="是否有WiFi")
    has_air_conditioning = Column(Boolean, default=True, comment="是否有空调")
    
    # 价格信息
    minimum_charge = Column(Numeric(10, 2), nullable=True, comment="最低消费")
    service_charge_rate = Column(Numeric(5, 2), nullable=True, comment="服务费率(%)")
    hourly_rate = Column(Numeric(10, 2), nullable=True, comment="包厢时费")
    
    # 预订和使用信息
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_vip_only = Column(Boolean, default=False, comment="是否仅限VIP")
    requires_reservation = Column(Boolean, default=False, comment="是否需要预订")
    
    # 当前使用信息
    current_guests = Column(Integer, default=0, comment="当前客人数")
    occupied_since = Column(DateTime(timezone=True), nullable=True, comment="开始使用时间")
    estimated_duration = Column(Integer, nullable=True, comment="预计用餐时长(分钟)")
    
    # 负责服务员
    assigned_waiter_id = Column(Integer, nullable=True, comment="分配的服务员ID")
    
    # 备注信息
    notes = Column(Text, nullable=True, comment="备注")
    special_requirements = Column(Text, nullable=True, comment="特殊要求")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    orders = relationship("Order", back_populates="table")
    
    def __repr__(self):
        return f"<Table(id={self.id}, number='{self.number}', status='{self.status}')>"
    
    @property
    def is_available(self) -> bool:
        """是否可用"""
        return self.status == TableStatus.AVAILABLE and self.is_active
    
    @property
    def is_occupied(self) -> bool:
        """是否被占用"""
        return self.status in [TableStatus.OCCUPIED, TableStatus.RESERVED]
    
    @property
    def display_name(self) -> str:
        """显示名称"""
        if self.name:
            return f"{self.number} - {self.name}"
        return self.number
    
    @property
    def facilities_list(self) -> list:
        """设施列表"""
        facilities = []
        if self.has_tv:
            facilities.append("电视")
        if self.has_karaoke:
            facilities.append("KTV")
        if self.has_mahjong:
            facilities.append("麻将")
        if self.has_projector:
            facilities.append("投影仪")
        if self.has_wifi:
            facilities.append("WiFi")
        if self.has_air_conditioning:
            facilities.append("空调")
        return facilities
    
    def can_accommodate(self, guest_count: int) -> bool:
        """是否能容纳指定人数"""
        if self.min_capacity and guest_count < self.min_capacity:
            return False
        return guest_count <= self.capacity
    
    def get_service_charge(self, amount: Numeric) -> Numeric:
        """计算服务费"""
        if self.service_charge_rate:
            return amount * (self.service_charge_rate / 100)
        return 0
