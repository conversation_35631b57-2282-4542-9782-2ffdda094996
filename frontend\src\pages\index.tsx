import React from 'react';
import { Card, Row, Col, Statistic, Typography, Space, Button, List, Tag, Progress } from 'antd';
import {
  TableOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  TeamOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  FireOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import MainLayout from '@/components/Layout/MainLayout';
import { useAuthStore } from '@/store';
import { formatCurrency, formatDateTime } from '@/utils/helpers';

const { Title, Text } = Typography;

const DashboardPage: React.FC = () => {
  const { user } = useAuthStore();

  // 模拟数据
  const stats = {
    totalTables: 15,
    occupiedTables: 8,
    todayOrders: 45,
    todayRevenue: 12580.50,
    activeStaff: 12,
    completionRate: 95,
  };

  const recentOrders = [
    {
      id: 1,
      orderNumber: 'JY20241215001',
      tableNumber: 'A01',
      status: 'cooking',
      items: 3,
      amount: 268.00,
      time: '2024-12-15 12:30:00',
    },
    {
      id: 2,
      orderNumber: 'JY20241215002',
      tableNumber: 'VIP01',
      status: 'ready',
      items: 8,
      amount: 580.00,
      time: '2024-12-15 12:25:00',
    },
    {
      id: 3,
      orderNumber: 'JY20241215003',
      tableNumber: 'B02',
      status: 'served',
      items: 5,
      amount: 320.00,
      time: '2024-12-15 12:20:00',
    },
  ];

  const popularDishes = [
    { name: '暨阳湖大闸蟹', sales: 15, trend: 'up' },
    { name: '红烧狮子头', sales: 12, trend: 'up' },
    { name: '宫保鸡丁', sales: 10, trend: 'stable' },
    { name: '糖醋里脊', sales: 8, trend: 'down' },
    { name: '麻婆豆腐', sales: 7, trend: 'up' },
  ];

  const getStatusColor = (status: string) => {
    const colors = {
      cooking: 'processing',
      ready: 'warning',
      served: 'success',
      cancelled: 'error',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = {
      cooking: '制作中',
      ready: '待上菜',
      served: '已上菜',
      cancelled: '已取消',
    };
    return texts[status as keyof typeof texts] || status;
  };

  const renderManagementDashboard = () => (
    <>
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={12} sm={12} md={6}>
          <Card>
            <Statistic
              title="餐桌总数"
              value={stats.totalTables}
              prefix={<TableOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6}>
          <Card>
            <Statistic
              title="使用中"
              value={stats.occupiedTables}
              prefix={<TableOutlined />}
              valueStyle={{ color: '#f5222d' }}
              suffix={`/ ${stats.totalTables}`}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6}>
          <Card>
            <Statistic
              title="今日订单"
              value={stats.todayOrders}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6}>
          <Card>
            <Statistic
              title="今日营业额"
              value={stats.todayRevenue}
              prefix={<DollarOutlined />}
              precision={2}
              valueStyle={{ color: '#faad14' }}
              formatter={(value) => formatCurrency(Number(value))}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 最近订单 */}
        <Col xs={24} lg={14}>
          <Card
            title="最近订单"
            extra={
              <Button type="link" href="/orders">
                查看全部
              </Button>
            }
          >
            <List
              dataSource={recentOrders}
              renderItem={(order) => (
                <List.Item
                  actions={[
                    <Tag color={getStatusColor(order.status)} key="status">
                      {getStatusText(order.status)}
                    </Tag>,
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <Text strong>{order.orderNumber}</Text>
                        <Text type="secondary">桌号: {order.tableNumber}</Text>
                      </Space>
                    }
                    description={
                      <Space split="|">
                        <Text>{order.items} 道菜</Text>
                        <Text>{formatCurrency(order.amount)}</Text>
                        <Text>{formatDateTime(order.time, 'HH:mm')}</Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 热门菜品 */}
        <Col xs={24} lg={10}>
          <Card title="今日热门菜品" extra={<FireOutlined style={{ color: '#f5222d' }} />}>
            <List
              dataSource={popularDishes}
              renderItem={(dish, index) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <div className="w-8 h-8 bg-jiyang-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {index + 1}
                      </div>
                    }
                    title={dish.name}
                    description={`销量: ${dish.sales}`}
                  />
                  <div>
                    {dish.trend === 'up' && <Text type="success">↗</Text>}
                    {dish.trend === 'down' && <Text type="danger">↘</Text>}
                    {dish.trend === 'stable' && <Text type="secondary">→</Text>}
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </>
  );

  const renderWaiterDashboard = () => (
    <Row gutter={[16, 16]}>
      <Col xs={24} md={12}>
        <Card title="我的工作状态">
          <Space direction="vertical" className="w-full">
            <div className="flex justify-between">
              <Text>负责餐桌:</Text>
              <Text strong>A区 (A01-A05)</Text>
            </div>
            <div className="flex justify-between">
              <Text>当前订单:</Text>
              <Text strong>3 个</Text>
            </div>
            <div className="flex justify-between">
              <Text>今日服务:</Text>
              <Text strong>15 桌</Text>
            </div>
            <div className="flex justify-between">
              <Text>完成率:</Text>
              <Progress percent={stats.completionRate} size="small" />
            </div>
          </Space>
        </Card>
      </Col>

      <Col xs={24} md={12}>
        <Card title="待处理事项">
          <List
            dataSource={[
              { type: 'order', text: 'A01桌 - 新订单待确认', urgent: true },
              { type: 'serve', text: 'A03桌 - 宫保鸡丁待上菜', urgent: false },
              { type: 'bill', text: 'A05桌 - 客人要求结账', urgent: true },
            ]}
            renderItem={(item) => (
              <List.Item>
                <Space>
                  {item.urgent ? (
                    <ClockCircleOutlined style={{ color: '#f5222d' }} />
                  ) : (
                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  )}
                  <Text>{item.text}</Text>
                </Space>
              </List.Item>
            )}
          />
        </Card>
      </Col>
    </Row>
  );

  const renderKitchenDashboard = () => (
    <Row gutter={[16, 16]}>
      <Col xs={24} md={8}>
        <Card>
          <Statistic
            title="待制作"
            value={8}
            prefix={<ClockCircleOutlined />}
            valueStyle={{ color: '#faad14' }}
          />
        </Card>
      </Col>
      <Col xs={24} md={8}>
        <Card>
          <Statistic
            title="制作中"
            value={5}
            prefix={<FireOutlined />}
            valueStyle={{ color: '#f5222d' }}
          />
        </Card>
      </Col>
      <Col xs={24} md={8}>
        <Card>
          <Statistic
            title="待上菜"
            value={3}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
    </Row>
  );

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* 欢迎信息 */}
        <Card>
          <div className="flex items-center justify-between">
            <div>
              <Title level={3} className="mb-2">
                欢迎回来，{user?.full_name}！
              </Title>
              <Text type="secondary">
                今天是 {formatDateTime(new Date(), 'YYYY年MM月DD日 dddd')}，祝您工作愉快！
              </Text>
            </div>
            <div className="hidden md:block">
              <TrophyOutlined style={{ fontSize: 48, color: '#faad14' }} />
            </div>
          </div>
        </Card>

        {/* 根据用户角色显示不同的仪表板 */}
        {user?.is_management && renderManagementDashboard()}
        {user?.is_service_staff && renderWaiterDashboard()}
        {user?.is_kitchen_staff && renderKitchenDashboard()}
      </div>
    </MainLayout>
  );
};

export default DashboardPage;
