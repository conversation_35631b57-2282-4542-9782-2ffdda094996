{% extends "base.html" %}

{% block title %}个人资料 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-person-circle"></i>
        个人资料
    </h1>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">基本信息</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>姓名:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.full_name }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>用户名:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.username }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>角色:</strong>
                    </div>
                    <div class="col-sm-9">
                        <span class="badge bg-{% if user.role.value == 'admin' %}danger{% elif user.role.value == 'manager' %}warning{% elif user.role.value == 'waiter' %}primary{% elif user.role.value == 'chef_manager' %}success{% elif user.role.value == 'chef' %}info{% else %}secondary{% endif %}">
                            {% if user.role.value == 'admin' %}系统管理员
                            {% elif user.role.value == 'manager' %}餐饮经理
                            {% elif user.role.value == 'waiter' %}服务员
                            {% elif user.role.value == 'chef_manager' %}厨师长
                            {% elif user.role.value == 'chef' %}厨师
                            {% elif user.role.value == 'kitchen_helper' %}打荷员
                            {% elif user.role.value == 'cashier' %}收银员
                            {% else %}{{ user.role.value }}
                            {% endif %}
                        </span>
                    </div>
                </div>
                
                {% if user.employee_id %}
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>工号:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.employee_id }}
                    </div>
                </div>
                {% endif %}
                
                {% if user.department %}
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>部门:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.department }}
                    </div>
                </div>
                {% endif %}
                
                {% if user.position %}
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>职位:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.position }}
                    </div>
                </div>
                {% endif %}
                
                {% if user.email %}
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>邮箱:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.email }}
                    </div>
                </div>
                {% endif %}
                
                {% if user.phone %}
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>电话:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.phone }}
                    </div>
                </div>
                {% endif %}
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>注册时间:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                    </div>
                </div>
                
                {% if user.last_login_at %}
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>最后登录:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ user.last_login_at.strftime('%Y-%m-%d %H:%M:%S') }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">权限信息</h5>
            </div>
            <div class="card-body">
                <h6>可访问功能:</h6>
                <ul class="list-unstyled">
                    {% if user.has_permission('dashboard.view') %}
                    <li><i class="bi bi-check-circle text-success"></i> 工作台</li>
                    {% endif %}
                    
                    {% if user.has_permission('table.view') %}
                    <li><i class="bi bi-check-circle text-success"></i> 餐桌查看</li>
                    {% endif %}
                    
                    {% if user.has_permission('table.manage') %}
                    <li><i class="bi bi-check-circle text-success"></i> 餐桌管理</li>
                    {% endif %}
                    
                    {% if user.has_permission('menu.view') %}
                    <li><i class="bi bi-check-circle text-success"></i> 菜单查看</li>
                    {% endif %}
                    
                    {% if user.has_permission('menu.manage') %}
                    <li><i class="bi bi-check-circle text-success"></i> 菜单管理</li>
                    {% endif %}
                    
                    {% if user.has_permission('order.create') %}
                    <li><i class="bi bi-check-circle text-success"></i> 创建订单</li>
                    {% endif %}
                    
                    {% if user.has_permission('order.manage') %}
                    <li><i class="bi bi-check-circle text-success"></i> 订单管理</li>
                    {% endif %}
                    
                    {% if user.has_permission('kitchen.view') %}
                    <li><i class="bi bi-check-circle text-success"></i> 厨房操作</li>
                    {% endif %}
                    
                    {% if user.has_permission('user.manage') %}
                    <li><i class="bi bi-check-circle text-success"></i> 用户管理</li>
                    {% endif %}
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">修改密码</h5>
            </div>
            <div class="card-body">
                <form method="post" action="/profile/change-password">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">当前密码</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">新密码</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">确认新密码</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">修改密码</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
