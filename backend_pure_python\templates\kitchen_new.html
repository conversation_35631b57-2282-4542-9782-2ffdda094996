{% extends "base.html" %}

{% block title %}厨房操作 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block extra_css %}
<style>
    /* 菜品网格布局 */
    
    .dish-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 15px;
    }
    
    .dish-card {
        background: #fff;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
    }
    
    .dish-card:hover {
        border-color: #007bff;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,123,255,0.2);
    }
    
    .dish-card.completed {
        background: #f8f9fa;
        border-color: #28a745;
        opacity: 0.7;
    }

    .dish-card.completed .dish-name {
        text-decoration: line-through;
        color: #6c757d;
    }

    .dish-card.disabled {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-color: #dee2e6;
        opacity: 0.6;
        cursor: not-allowed;
        position: relative;
    }

    .dish-card.disabled::before {
        content: "等待用餐开始";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(108, 117, 125, 0.9);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: bold;
        z-index: 10;
    }

    .dish-card.disabled:hover {
        transform: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .dish-name {
        font-size: 1.2rem;
        font-weight: bold;
        margin-bottom: 8px;
        color: #333;
    }
    
    .dish-quantity {
        font-size: 1rem;
        color: #007bff;
        font-weight: 600;
        margin-bottom: 8px;
    }
    
    .dish-status {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        margin-bottom: 8px;
    }
    
    .status-pending_cook {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .status-ready {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .dish-time {
        font-size: 0.85rem;
        color: #6c757d;
        margin-top: 8px;
    }
    
    .special-requirements {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 5px;
        padding: 8px;
        margin-top: 8px;
        font-size: 0.9rem;
        color: #856404;
    }
    
    .completed-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background: #28a745;
        color: white;
        padding: 4px 8px;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: bold;
    }
    
    .no-rooms {
        text-align: center;
        padding: 100px 20px;
        color: #6c757d;
    }
    
    .no-rooms i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #dee2e6;
    }
    
    @media (max-width: 768px) {
        .main-content {
            margin-left: 0;
        }
        
        .dish-grid {
            grid-template-columns: 1fr;
        }
        
        .room-title {
            font-size: 1.5rem;
        }
    }

    /* 指令通知样式 */
    .command-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(255, 193, 7, 0.95);
        color: #000;
        border: 3px solid #ffc107;
        border-radius: 10px;
        padding: 15px 20px;
        z-index: 9999;
        max-width: 400px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        animation: slideInRight 0.5s ease-out;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .command-notification .notification-header {
        font-weight: bold;
        font-size: 1.2rem;
        margin-bottom: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .command-notification .notification-content {
        font-size: 1rem;
        margin-bottom: 10px;
    }

    .command-notification .notification-time {
        font-size: 0.9rem;
        opacity: 0.8;
        margin-bottom: 10px;
    }

    .command-notification .notification-actions {
        text-align: right;
    }

    .command-notification .btn {
        margin-left: 5px;
    }

    /* 指令悬浮窗样式 */
    .command-floating-window {
        position: fixed;
        top: 100px;
        right: 20px;
        width: 350px;
        max-height: 500px;
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid #dee2e6;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);
        z-index: 1000;
        transition: all 0.3s ease;
        cursor: move;
    }

    .command-floating-window:hover {
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
    }

    .floating-header {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 12px 15px;
        border-radius: 12px 12px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: move;
    }

    .floating-title {
        font-weight: 600;
        font-size: 0.95rem;
        display: flex;
        align-items: center;
    }

    .floating-controls {
        display: flex;
        gap: 5px;
    }

    .floating-controls .btn {
        padding: 4px 8px;
        border: none;
        background: rgba(255, 255, 255, 0.2);
    }

    .floating-controls .btn:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    .floating-content {
        max-height: 400px;
        overflow-y: auto;
        transition: all 0.3s ease;
    }

    .floating-content.minimized {
        max-height: 0;
        overflow: hidden;
    }

    .command-list {
        padding: 0;
    }

    .command-item {
        border-bottom: 1px solid #eee;
        padding: 12px 15px;
        transition: background-color 0.2s;
    }

    .command-item:hover {
        background-color: #f8f9fa;
    }

    .command-item:last-child {
        border-bottom: none;
    }

    .command-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
    }

    .command-room {
        font-weight: bold;
        color: #0d6efd;
    }

    .command-time {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .command-type {
        font-weight: 500;
        color: #198754;
    }

    .command-content {
        font-size: 0.9rem;
        color: #495057;
        margin-top: 3px;
    }

    .command-actions {
        margin-top: 8px;
        text-align: right;
    }

    .command-status {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .command-status.pending {
        background-color: #fff3cd;
        color: #856404;
    }

    .command-status.processed {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    /* 动画效果 */
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* 悬浮窗滚动条样式 */
    .floating-content::-webkit-scrollbar {
        width: 6px;
    }

    .floating-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .floating-content::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    .floating-content::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
</style>
{% endblock %}

{% block content %}
<!-- 弃用通知 -->
<div class="alert alert-warning alert-dismissible fade show" role="alert">
    <i class="bi bi-exclamation-triangle-fill"></i>
    <strong>页面已弃用：</strong> 此实验性页面已被新的打荷操作页面替代。
    <a href="/kitchen-helper" class="btn btn-sm btn-primary ms-2">
        <i class="bi bi-arrow-right"></i> 前往新版打荷操作页面
    </a>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-fire"></i>
        厨房操作 - 打荷员工作台 <span class="badge bg-warning text-dark">已弃用</span>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i>
                刷新
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleFullscreen()">
                <i class="bi bi-arrows-fullscreen"></i>
                全屏
            </button>
            <a href="/kitchen/display" class="btn btn-sm btn-primary" target="_blank">
                <i class="bi bi-display"></i>
                厨房大屏
            </a>
        </div>
    </div>
</div>

<!-- 服务员指令悬浮窗 -->
<div id="commandFloatingWindow" class="command-floating-window">
    <div class="floating-header">
        <div class="floating-title">
            <i class="bi bi-megaphone"></i> 服务员指令
            <span class="badge bg-primary ms-2" id="commandCount">0</span>
        </div>
        <div class="floating-controls">
            <button class="btn btn-sm btn-outline-light" onclick="toggleCommandWindow()" title="最小化/展开">
                <i class="bi bi-dash" id="toggleIcon"></i>
            </button>
            <button class="btn btn-sm btn-outline-light" onclick="refreshCommands()" title="刷新">
                <i class="bi bi-arrow-clockwise"></i>
            </button>
        </div>
    </div>
    <div id="commandFloatingContent" class="floating-content">
        <div id="commandList" class="command-list">
            <div class="text-center py-3 text-muted">
                <i class="bi bi-inbox"></i>
                <div>暂无未处理指令</div>
            </div>
        </div>
    </div>
</div>

{% if orders_by_room %}
{% for room_name, room_data in orders_by_room.items() %}
{% if room_data is mapping %}
    {% set dish_items = room_data.dish_items %}
    {% set is_served = room_data.is_served %}
    {% set can_operate = room_data.can_operate %}
    {% set room_info = room_data.room_info %}
{% else %}
    {% set dish_items = room_data %}
    {% set is_served = false %}
    {% set can_operate = false %}
    {% set room_info = {} %}
{% endif %}

<!-- 包厢卡片 -->
{% if dish_items %}
<div class="card shadow mb-4">
    <div class="card-header {% if can_operate %}bg-primary{% else %}bg-secondary{% endif %} text-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="bi bi-house-door"></i>
                {{ room_name }}
                {% if not can_operate %}
                <span class="badge bg-warning text-dark ms-2">
                    <i class="bi bi-clock"></i> 等待用餐开始
                </span>
                {% endif %}
            </h5>
            <div class="d-flex gap-3">
                <span><i class="bi bi-people"></i> {{ room_info.guest_count or 0 }}人</span>
                <span><i class="bi bi-currency-yen"></i> {{ room_info.dining_standard or 0 }}元/桌</span>
                {% if room_info.dining_start_time %}
                <span><i class="bi bi-clock"></i> {{ room_info.dining_start_time }}</span>
                {% endif %}
            </div>
        </div>
    </div>

    {% if room_info.special_requirements %}
    <div class="card-body bg-warning-subtle">
        <div class="alert alert-warning mb-0">
            <i class="bi bi-exclamation-triangle"></i>
            <strong>特殊要求:</strong> {{ room_info.special_requirements }}
        </div>
    </div>
    {% endif %}

    <div class="card-body">
        <div class="dish-grid">
            {% for item in dish_items %}
            <div class="dish-card {% if item.status.value == 'ready' %}completed{% endif %}{% if not can_operate %} disabled{% endif %}"
                 {% if can_operate %}onclick="markDishCompleted({{ item.id }}, '{{ item.dish_name }}', '{{ room_name }}', '{{ item.status.value }}', {{ can_operate|lower }})"{% else %}onclick="showWaitingMessage('{{ room_name }}')"{% endif %}>

                {% if item.status.value == 'ready' %}
                <div class="completed-badge">
                    <i class="bi bi-check"></i> 制作完成
                </div>
                {% endif %}

                <div class="dish-name">{{ item.dish_name }}</div>
                <div class="dish-quantity">{{ item.quantity }}份</div>

                <div class="dish-status status-{{ item.status.value }}">
                    {% if item.status.value == 'pending_cook' %}
                        <i class="bi bi-clock"></i> 待制作
                    {% elif item.status.value == 'ready' %}
                        <i class="bi bi-check-all"></i> 制作完成
                    {% endif %}
                </div>

                {% if item.special_requirements %}
                <div class="special-requirements">
                    <i class="bi bi-exclamation-triangle"></i>
                    {{ item.special_requirements }}
                </div>
                {% endif %}

                <div class="dish-time">
                    下单时间: {{ item.created_at.strftime('%H:%M') }}
                    {% if item.ready_at %}
                    <br>完成时间: {{ item.ready_at.strftime('%H:%M') }}
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}
{% endfor %}
{% else %}
<div class="card shadow">
    <div class="card-body text-center py-5">
        <i class="bi bi-inbox text-muted" style="font-size: 4rem;"></i>
        <h4 class="text-muted mt-3">暂无预订包厢</h4>
        <p class="text-muted">当前没有需要制作的菜品</p>
    </div>
</div>
{% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>

    
    // 检查菜品是否可以操作
    function canOperateDish(itemId, currentStatus) {
        // 已完成的菜品不能再操作
        if (currentStatus === 'ready') {
            return { canOperate: false, reason: '菜品已制作完成，无需重复操作' };
        }

        // 只允许待制作的菜品进行操作
        if (currentStatus === 'pending_cook') {
            return { canOperate: true, reason: '' };
        }

        return { canOperate: false, reason: '菜品状态异常，无法操作' };
    }

    // 显示等待用餐开始的提示
    function showWaitingMessage(roomName) {
        showErrorMessage(`${roomName} 包厢尚未开始用餐，请等待服务员发出"开始用餐"指令后再进行厨房操作`);
    }

    // 标记菜品完成
    function markDishCompleted(itemId, dishName, roomName, currentStatus, canOperate = true) {
        console.log(`🔍 菜品操作检查: ${dishName} (${currentStatus}), 可操作: ${canOperate}`);

        // 检查包厢是否可以操作
        if (!canOperate) {
            showWaitingMessage(roomName);
            return;
        }

        // 检查是否可以操作
        const operationCheck = canOperateDish(itemId, currentStatus);
        if (!operationCheck.canOperate) {
            showErrorMessage(operationCheck.reason);
            return;
        }

        if (!confirm(`确认完成制作：${dishName}？\n\n点击确认后菜品将标记为制作完成，并触发跑菜语音播报。`)) {
            return;
        }

        // 显示处理中状态
        const dishCard = document.querySelector(`[onclick*="${itemId}"]`);
        if (dishCard) {
            dishCard.style.opacity = '0.6';
            dishCard.style.pointerEvents = 'none';

            // 添加处理中提示
            const processingIndicator = document.createElement('div');
            processingIndicator.className = 'processing-indicator';
            processingIndicator.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 处理中...';
            processingIndicator.style.cssText = 'position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 5px 10px; border-radius: 5px; font-size: 0.8rem;';
            dishCard.style.position = 'relative';
            dishCard.appendChild(processingIndicator);
        }

        console.log(`📤 发送菜品状态更新请求: ${itemId} -> ready`);

        fetch(`/kitchen/items/${itemId}/status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: 'ready'  // 改为ready状态，触发跑菜语音
            })
        })
        .then(response => {
            console.log(`📥 收到响应: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                if (response.status === 403) {
                    throw new Error('权限不足：请等待服务员发出上菜指令后再进行操作');
                } else if (response.status === 404) {
                    throw new Error('菜品不存在或已被删除');
                } else {
                    throw new Error(`服务器错误 (${response.status}): ${response.statusText}`);
                }
            }
            return response.json();
        })
        .then(data => {
            console.log('📋 API响应数据:', data);

            if (data.success) {
                console.log('✅ 菜品制作完成:', dishName);

                // 播放跑菜语音（播报2次）
                if (data.voice_message && 'speechSynthesis' in window) {
                    playDishReadyVoice(data.voice_message, data.voice_config?.repeat_count || 2);
                }

                // 显示成功提示
                showSuccessMessage(`${dishName} 制作完成，已触发跑菜语音播报`);

                // 1.5秒后刷新页面
                setTimeout(() => location.reload(), 1500);
            } else {
                throw new Error(data.message || data.detail || '操作失败，请重试');
            }
        })
        .catch(error => {
            console.error('❌ 菜品制作确认失败:', error);

            // 恢复卡片状态
            if (dishCard) {
                dishCard.style.opacity = '1';
                dishCard.style.pointerEvents = 'auto';

                // 移除处理中提示
                const processingIndicator = dishCard.querySelector('.processing-indicator');
                if (processingIndicator) {
                    processingIndicator.remove();
                }
            }

            // 显示具体错误信息
            const errorMsg = error.message || '网络连接失败';
            showErrorMessage(`操作失败: ${errorMsg}`);
        });
    }

    // 播放跑菜语音（指定次数）
    async function playDishReadyVoice(message, repeatCount = 2) {
        for (let i = 0; i < repeatCount; i++) {
            await new Promise((resolve) => {
                const utterance = new SpeechSynthesisUtterance(message);
                utterance.lang = 'zh-CN';
                utterance.rate = 0.8;
                utterance.volume = 1.0;
                utterance.pitch = 1.0;

                utterance.onend = () => resolve();
                utterance.onerror = () => resolve();

                speechSynthesis.speak(utterance);
            });

            // 两次播报之间间隔4秒
            if (i < repeatCount - 1) {
                await new Promise(resolve => setTimeout(resolve, 4000));
            }
        }
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
        successDiv.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px;';
        successDiv.innerHTML = `
            <i class="bi bi-check-circle"></i> ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        document.body.appendChild(successDiv);

        // 5秒后自动移除
        setTimeout(() => {
            if (successDiv.parentElement) {
                successDiv.remove();
            }
        }, 5000);
    }

    // 显示错误消息
    function showErrorMessage(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        errorDiv.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px; max-width: 500px;';
        errorDiv.innerHTML = `
            <i class="bi bi-exclamation-triangle"></i> ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        document.body.appendChild(errorDiv);

        // 8秒后自动移除
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 8000);
    }
    
    // 检查新指令
    function checkNewCommands() {
        fetch('/api/waiter-actions/latest')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.actions) {
                    updateCommandList(data.actions);
                }
            })
            .catch(error => {
                console.error('检查指令失败:', error);
            });
    }

    // 更新指令列表
    function updateCommandList(actions) {
        const commandList = document.getElementById('commandList');
        const commandCount = document.getElementById('commandCount');

        // 过滤未处理的指令并按时间排序
        const unprocessedActions = actions.filter(action => !action.is_processed)
            .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
            .slice(0, 10); // 只显示最近10条

        commandCount.textContent = unprocessedActions.length;

        if (unprocessedActions.length === 0) {
            commandList.innerHTML = `
                <div class="text-center py-3 text-muted">
                    <i class="bi bi-inbox"></i>
                    <div>暂无未处理指令</div>
                </div>
            `;
            return;
        }

        const commandsHtml = unprocessedActions.map(action => `
            <div class="command-item" data-action-id="${action.id}">
                <div class="command-header">
                    <span class="command-room">${action.room_number}包厢</span>
                    <span class="command-time">${new Date(action.created_at).toLocaleTimeString()}</span>
                </div>
                <div class="command-type">${action.action_type_display}</div>
                ${action.action_content ? `<div class="command-content">${action.action_content}</div>` : ''}
                <div class="command-actions">
                    <span class="command-status pending">待处理</span>
                    <button type="button" class="btn btn-sm btn-success ms-2"
                            onclick="processCommandFromList(${action.id}, this)">
                        <i class="bi bi-check"></i> 处理
                    </button>
                </div>
            </div>
        `).join('');

        commandList.innerHTML = commandsHtml;
    }

    // 从列表处理指令
    function processCommandFromList(actionId, button) {
        fetch('/kitchen/actions/' + actionId + '/processed', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => {
            if (response.ok) {
                const commandItem = button.closest('.command-item');
                const statusSpan = commandItem.querySelector('.command-status');

                statusSpan.textContent = '已处理';
                statusSpan.className = 'command-status processed';
                button.innerHTML = '<i class="bi bi-check-circle"></i> 已处理';
                button.disabled = true;
                button.className = 'btn btn-sm btn-outline-success ms-2';

                // 2秒后移除该项
                setTimeout(() => {
                    commandItem.style.transition = 'opacity 0.3s';
                    commandItem.style.opacity = '0';
                    setTimeout(() => {
                        commandItem.remove();
                        // 更新计数
                        const currentCount = parseInt(document.getElementById('commandCount').textContent);
                        document.getElementById('commandCount').textContent = Math.max(0, currentCount - 1);

                        // 如果没有指令了，显示空状态
                        if (document.querySelectorAll('.command-item').length === 0) {
                            document.getElementById('commandList').innerHTML = `
                                <div class="text-center py-3 text-muted">
                                    <i class="bi bi-inbox"></i>
                                    <div>暂无未处理指令</div>
                                </div>
                            `;
                        }
                    }, 300);
                }, 2000);
            } else {
                alert('处理失败');
            }
        }).catch(error => {
            console.error('处理指令失败:', error);
            alert('网络错误');
        });
    }

    // 显示指令通知
    function showCommandNotification(action) {
        // 移除已存在的通知
        const existingNotification = document.querySelector('.command-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        const notification = document.createElement('div');
        notification.className = 'command-notification';
        notification.innerHTML = `
            <div class="notification-header">
                <i class="bi bi-megaphone"></i> ${action.room_number}包厢指令
                <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
            <div class="notification-content">
                <strong>${action.action_type_display}</strong>
                ${action.action_content ? '<br><small>' + action.action_content + '</small>' : ''}
            </div>
            <div class="notification-time">
                <i class="bi bi-clock"></i> ${new Date(action.created_at).toLocaleTimeString()}
            </div>
            <div class="notification-actions">
                <button type="button" class="btn btn-sm btn-success" onclick="confirmKitchenCommand(${action.id}, this)">
                    <i class="bi bi-check"></i> 确认
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // 8秒后自动关闭
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 8000);
    }

    // 确认厨房指令
    function confirmKitchenCommand(actionId, button) {
        fetch('/kitchen/actions/' + actionId + '/processed', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => {
            if (response.ok) {
                button.innerHTML = '<i class="bi bi-check-circle"></i> 已确认';
                button.disabled = true;
                button.className = 'btn btn-sm btn-outline-success';

                setTimeout(() => {
                    button.parentElement.parentElement.remove();
                }, 2000);
            } else {
                alert('确认失败');
            }
        });
    }

    // 全屏功能
    function toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().then(() => {
                const btn = document.querySelector('.btn-outline-secondary');
                if (btn && btn.innerHTML.includes('arrows-fullscreen')) {
                    btn.innerHTML = '<i class="bi bi-fullscreen-exit"></i> 退出全屏';
                }
            }).catch(err => {
                console.error('无法进入全屏模式:', err);
            });
        } else {
            document.exitFullscreen().then(() => {
                const btn = document.querySelector('.btn-outline-secondary');
                if (btn && btn.innerHTML.includes('fullscreen-exit')) {
                    btn.innerHTML = '<i class="bi bi-arrows-fullscreen"></i> 全屏';
                }
            });
        }
    }

    // 监听全屏状态变化
    document.addEventListener('fullscreenchange', function() {
        const btn = document.querySelector('.btn-outline-secondary');
        if (btn) {
            if (document.fullscreenElement) {
                btn.innerHTML = '<i class="bi bi-fullscreen-exit"></i> 退出全屏';
            } else {
                btn.innerHTML = '<i class="bi bi-arrows-fullscreen"></i> 全屏';
            }
        }
    });

    // F11快捷键支持
    document.addEventListener('keydown', function(e) {
        if (e.key === 'F11') {
            e.preventDefault();
            toggleFullscreen();
        }
    });

    // 悬浮窗拖拽功能
    let isDragging = false;
    let dragOffset = { x: 0, y: 0 };

    function initFloatingWindow() {
        const floatingWindow = document.getElementById('commandFloatingWindow');
        const header = floatingWindow.querySelector('.floating-header');

        header.addEventListener('mousedown', function(e) {
            isDragging = true;
            const rect = floatingWindow.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;

            document.addEventListener('mousemove', handleDrag);
            document.addEventListener('mouseup', stopDrag);

            floatingWindow.style.cursor = 'grabbing';
            e.preventDefault();
        });
    }

    function handleDrag(e) {
        if (!isDragging) return;

        const floatingWindow = document.getElementById('commandFloatingWindow');
        const newX = e.clientX - dragOffset.x;
        const newY = e.clientY - dragOffset.y;

        // 限制拖拽范围
        const maxX = window.innerWidth - floatingWindow.offsetWidth;
        const maxY = window.innerHeight - floatingWindow.offsetHeight;

        const constrainedX = Math.max(0, Math.min(newX, maxX));
        const constrainedY = Math.max(0, Math.min(newY, maxY));

        floatingWindow.style.left = constrainedX + 'px';
        floatingWindow.style.top = constrainedY + 'px';
        floatingWindow.style.right = 'auto';
    }

    function stopDrag() {
        isDragging = false;
        document.removeEventListener('mousemove', handleDrag);
        document.removeEventListener('mouseup', stopDrag);

        const floatingWindow = document.getElementById('commandFloatingWindow');
        floatingWindow.style.cursor = 'move';
    }

    // 切换悬浮窗最小化状态
    function toggleCommandWindow() {
        const content = document.getElementById('commandFloatingContent');
        const icon = document.getElementById('toggleIcon');

        if (content.classList.contains('minimized')) {
            content.classList.remove('minimized');
            icon.className = 'bi bi-dash';
        } else {
            content.classList.add('minimized');
            icon.className = 'bi bi-plus';
        }
    }

    // 刷新指令
    function refreshCommands() {
        checkNewCommands();

        // 显示刷新动画
        const refreshBtn = document.querySelector('.floating-controls .bi-arrow-clockwise');
        refreshBtn.style.animation = 'spin 0.5s linear';
        setTimeout(() => {
            refreshBtn.style.animation = '';
        }, 500);
    }

    // 初始化悬浮窗
    document.addEventListener('DOMContentLoaded', function() {
        initFloatingWindow();
    });

    // 页面加载时检查指令
    checkNewCommands();

    // 定期检查新指令
    setInterval(checkNewCommands, 5000);

    // 自动刷新
    setInterval(() => {
        location.reload();
    }, 60000); // 每分钟刷新一次
</script>
{% endblock %}
