<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 暨阳湖大酒店传菜管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/static/css/bootstrap-icons.css" rel="stylesheet">
    <!-- 暨阳湖增强样式 -->
    <link href="/static/css/jiyang-enhanced.css" rel="stylesheet">
    
    <style>
        :root {
            --jiyang-primary: #f2750a;
            --jiyang-secondary: #e35d05;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .login-header {
            background: var(--jiyang-primary);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-header h1 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }
        
        .login-header p {
            margin: 0;
            opacity: 0.9;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .btn-primary {
            background-color: var(--jiyang-primary);
            border-color: var(--jiyang-primary);
            width: 100%;
            padding: 0.75rem;
        }
        
        .btn-primary:hover {
            background-color: var(--jiyang-secondary);
            border-color: var(--jiyang-secondary);
        }
        
        .quick-login {
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #dee2e6;
        }
        
        .quick-login h6 {
            color: #6c757d;
            margin-bottom: 1rem;
        }
        
        .quick-login .btn {
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <i class="bi bi-building" style="font-size: 2rem; margin-bottom: 1rem;"></i>
            <h1>暨阳湖大酒店</h1>
            <p>传菜管理系统</p>
        </div>
        
        <div class="login-body">
            {% if error %}
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle"></i>
                {{ error }}
            </div>
            {% endif %}
            
            <form method="post" action="/login">
                <div class="mb-3">
                    <label for="username" class="form-label">
                        <i class="bi bi-person"></i>
                        用户名
                    </label>
                    <input type="text" class="form-control" id="username" name="username" required>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">
                        <i class="bi bi-lock"></i>
                        密码
                    </label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-box-arrow-in-right"></i>
                    登录
                </button>
            </form>
            
            <div class="quick-login">
                <h6 class="text-center">快速登录</h6>
                
                <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="quickLogin('admin', 'admin123')">
                    <i class="bi bi-shield-check"></i>
                    管理员登录 (admin/admin123)
                </button>
                
                <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="quickLogin('manager01', 'manager123')">
                    <i class="bi bi-person-badge"></i>
                    经理登录 (manager01/manager123)
                </button>
                
                <button type="button" class="btn btn-outline-info btn-sm w-100" onclick="quickLogin('waiter01', 'waiter123')">
                    <i class="bi bi-person"></i>
                    服务员登录 (waiter01/waiter123)
                </button>
                
                <button type="button" class="btn btn-outline-warning btn-sm w-100" onclick="quickLogin('chef01', 'chef123')">
                    <i class="bi bi-person-workspace"></i>
                    厨师长登录 (chef01/chef123)
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function quickLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            document.querySelector('form').submit();
        }
        
        // 自动隐藏错误消息
        setTimeout(function() {
            var alert = document.querySelector('.alert');
            if (alert) {
                alert.style.display = 'none';
            }
        }, 5000);
    </script>
</body>
</html>
