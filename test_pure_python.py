#!/usr/bin/env python3
"""
纯Python版本系统测试脚本
测试Web应用的基本功能
"""

import requests
import time
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

class PurePythonTester:
    def __init__(self):
        self.session = requests.Session()
        self.cookies = {}
    
    def test_home_redirect(self) -> bool:
        """测试首页重定向"""
        try:
            response = self.session.get(f"{BASE_URL}/", allow_redirects=False)
            if response.status_code in [302, 307]:
                print("✅ 首页重定向正常")
                return True
            else:
                print(f"❌ 首页重定向失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 首页访问异常: {e}")
            return False
    
    def test_login_page(self) -> bool:
        """测试登录页面"""
        try:
            response = self.session.get(f"{BASE_URL}/login")
            if response.status_code == 200 and "暨阳湖大酒店" in response.text:
                print("✅ 登录页面加载成功")
                return True
            else:
                print(f"❌ 登录页面加载失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 登录页面访问异常: {e}")
            return False
    
    def test_login(self, username: str = "admin", password: str = "admin123") -> bool:
        """测试用户登录"""
        try:
            data = {
                "username": username,
                "password": password
            }
            response = self.session.post(f"{BASE_URL}/login", data=data, allow_redirects=False)
            
            if response.status_code in [302, 307]:
                # 检查是否设置了cookie
                if 'access_token' in response.cookies:
                    self.cookies['access_token'] = response.cookies['access_token']
                    print(f"✅ 登录成功: {username}")
                    return True
                else:
                    print(f"❌ 登录失败: 未设置认证cookie")
                    return False
            else:
                print(f"❌ 登录失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def test_dashboard(self) -> bool:
        """测试工作台页面"""
        try:
            response = self.session.get(f"{BASE_URL}/dashboard")
            
            if response.status_code == 200 and "工作台" in response.text:
                print("✅ 工作台页面加载成功")
                return True
            elif response.status_code == 302:
                print("❌ 工作台访问被重定向（可能未登录）")
                return False
            else:
                print(f"❌ 工作台页面加载失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 工作台页面访问异常: {e}")
            return False
    
    def test_tables_page(self) -> bool:
        """测试餐桌管理页面"""
        try:
            response = self.session.get(f"{BASE_URL}/tables")
            
            if response.status_code == 200 and "餐桌管理" in response.text:
                print("✅ 餐桌管理页面加载成功")
                return True
            elif response.status_code == 302:
                print("❌ 餐桌管理页面访问被重定向（可能未登录）")
                return False
            else:
                print(f"❌ 餐桌管理页面加载失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 餐桌管理页面访问异常: {e}")
            return False
    
    def test_menu_page(self) -> bool:
        """测试菜单管理页面"""
        try:
            response = self.session.get(f"{BASE_URL}/menu")
            
            if response.status_code == 200 and "菜单管理" in response.text:
                print("✅ 菜单管理页面加载成功")
                return True
            elif response.status_code == 302:
                print("❌ 菜单管理页面访问被重定向（可能未登录）")
                return False
            else:
                print(f"❌ 菜单管理页面加载失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 菜单管理页面访问异常: {e}")
            return False
    
    def test_orders_page(self) -> bool:
        """测试订单管理页面"""
        try:
            response = self.session.get(f"{BASE_URL}/orders")
            
            if response.status_code == 200 and "订单管理" in response.text:
                print("✅ 订单管理页面加载成功")
                return True
            elif response.status_code == 302:
                print("❌ 订单管理页面访问被重定向（可能未登录）")
                return False
            else:
                print(f"❌ 订单管理页面加载失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 订单管理页面访问异常: {e}")
            return False
    
    def test_create_order_page(self) -> bool:
        """测试创建订单页面"""
        try:
            response = self.session.get(f"{BASE_URL}/orders/create")
            
            if response.status_code == 200 and "新建订单" in response.text:
                print("✅ 新建订单页面加载成功")
                return True
            elif response.status_code == 302:
                print("❌ 新建订单页面访问被重定向（可能未登录）")
                return False
            else:
                print(f"❌ 新建订单页面加载失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 新建订单页面访问异常: {e}")
            return False
    
    def test_logout(self) -> bool:
        """测试登出功能"""
        try:
            response = self.session.get(f"{BASE_URL}/logout", allow_redirects=False)
            
            if response.status_code in [302, 307]:
                print("✅ 登出成功")
                return True
            else:
                print(f"❌ 登出失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 登出异常: {e}")
            return False
    
    def test_different_roles(self) -> bool:
        """测试不同角色登录"""
        roles = [
            ("manager01", "manager123", "经理"),
            ("waiter01", "waiter123", "服务员"),
            ("chef01", "chef123", "厨师长"),
        ]
        
        success_count = 0
        for username, password, role_name in roles:
            print(f"\n🧪 测试{role_name}登录...")
            if self.test_login(username, password):
                success_count += 1
                # 测试工作台访问
                if self.test_dashboard():
                    print(f"✅ {role_name}可以正常访问工作台")
                else:
                    print(f"❌ {role_name}无法访问工作台")
                # 登出
                self.test_logout()
            time.sleep(1)
        
        print(f"\n✅ 角色测试完成: {success_count}/{len(roles)} 个角色登录成功")
        return success_count == len(roles)
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("暨阳湖大酒店传菜管理系统 - 纯Python版本测试")
        print("=" * 60)
        
        tests = [
            ("首页重定向", self.test_home_redirect),
            ("登录页面", self.test_login_page),
            ("管理员登录", self.test_login),
            ("工作台页面", self.test_dashboard),
            ("餐桌管理页面", self.test_tables_page),
            ("菜单管理页面", self.test_menu_page),
            ("订单管理页面", self.test_orders_page),
            ("新建订单页面", self.test_create_order_page),
            ("登出功能", self.test_logout),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🧪 测试: {test_name}")
            try:
                if test_func():
                    passed += 1
                time.sleep(0.5)  # 避免请求过快
            except Exception as e:
                print(f"❌ 测试异常: {e}")
        
        # 测试不同角色
        print(f"\n🧪 测试: 不同角色登录")
        if self.test_different_roles():
            passed += 1
        total += 1
        
        print("\n" + "=" * 60)
        print(f"测试完成: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！纯Python版本运行正常")
            print("\n✨ 系统功能:")
            print("📱 Web界面: http://localhost:8000")
            print("👤 用户登录: 支持多角色认证")
            print("🏨 餐桌管理: 查看和管理餐桌状态")
            print("📖 菜单管理: 浏览和管理菜品信息")
            print("🛒 订单管理: 创建和管理订单")
            print("📊 工作台: 实时数据统计")
        else:
            print("⚠️  部分测试失败，请检查系统状态")
        
        print("=" * 60)
        
        return passed == total


def main():
    """主函数"""
    print("等待服务启动...")
    time.sleep(2)  # 等待服务启动
    
    tester = PurePythonTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ 纯Python版本测试完成，系统运行正常")
        print("🌐 访问地址: http://localhost:8000")
        print("📚 API 文档: http://localhost:8000/docs")
        print("\n🔑 默认登录账号:")
        print("管理员: admin / admin123")
        print("经理: manager01 / manager123")
        print("服务员: waiter01 / waiter123")
        print("厨师长: chef01 / chef123")
    else:
        print("\n❌ 系统测试失败，请检查服务状态")
        print("💡 确保服务已启动且数据库已初始化")
        print("🔧 运行: python start_pure_python.py")


if __name__ == "__main__":
    main()
