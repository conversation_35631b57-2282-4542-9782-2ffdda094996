#!/usr/bin/env python3
"""
性能测试脚本
"""

import time
import requests
import concurrent.futures
from typing import List

BASE_URL = "http://localhost:8000"

def test_login_performance():
    """测试登录性能"""
    print("🧪 测试登录性能...")
    
    start_time = time.time()
    
    # 测试登录
    session = requests.Session()
    response = session.post(f"{BASE_URL}/login", data={
        "username": "admin",
        "password": "admin123"
    }, allow_redirects=False)
    
    end_time = time.time()
    
    if response.status_code in [302, 307]:
        print(f"✅ 登录成功，耗时: {(end_time - start_time)*1000:.2f}ms")
        return session
    else:
        print(f"❌ 登录失败: {response.status_code}")
        return None

def test_page_performance(session: requests.Session, page_name: str, url: str):
    """测试页面加载性能"""
    start_time = time.time()
    
    try:
        response = session.get(url)
        end_time = time.time()
        
        if response.status_code == 200:
            load_time = (end_time - start_time) * 1000
            print(f"✅ {page_name}: {load_time:.2f}ms")
            return load_time
        else:
            print(f"❌ {page_name}: HTTP {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ {page_name}: 错误 - {e}")
        return None

def test_concurrent_requests(session: requests.Session, url: str, num_requests: int = 10):
    """测试并发请求性能"""
    print(f"🧪 测试并发性能 ({num_requests}个并发请求)...")
    
    def make_request():
        start_time = time.time()
        try:
            response = session.get(url)
            end_time = time.time()
            return (end_time - start_time) * 1000, response.status_code
        except Exception as e:
            return None, str(e)
    
    start_time = time.time()
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_requests) as executor:
        futures = [executor.submit(make_request) for _ in range(num_requests)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    end_time = time.time()
    
    successful_requests = [r for r in results if r[0] is not None and r[1] == 200]
    
    if successful_requests:
        response_times = [r[0] for r in successful_requests]
        avg_time = sum(response_times) / len(response_times)
        min_time = min(response_times)
        max_time = max(response_times)
        total_time = (end_time - start_time) * 1000
        
        print(f"✅ 并发测试完成:")
        print(f"   总耗时: {total_time:.2f}ms")
        print(f"   成功请求: {len(successful_requests)}/{num_requests}")
        print(f"   平均响应时间: {avg_time:.2f}ms")
        print(f"   最快响应: {min_time:.2f}ms")
        print(f"   最慢响应: {max_time:.2f}ms")
        print(f"   QPS: {len(successful_requests) / (total_time/1000):.2f}")
    else:
        print("❌ 并发测试失败")

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 暨阳湖大酒店传菜管理系统 - 性能测试")
    print("=" * 60)
    
    # 测试登录
    session = test_login_performance()
    if not session:
        print("❌ 无法登录，终止测试")
        return
    
    print("\n📊 页面加载性能测试:")
    print("-" * 40)
    
    # 测试各个页面的性能
    pages = [
        ("工作台", f"{BASE_URL}/dashboard"),
        ("餐桌管理", f"{BASE_URL}/tables"),
        ("菜单管理", f"{BASE_URL}/menu"),
        ("订单管理", f"{BASE_URL}/orders"),
        ("新建订单", f"{BASE_URL}/orders/create"),
    ]
    
    load_times = []
    for page_name, url in pages:
        load_time = test_page_performance(session, page_name, url)
        if load_time:
            load_times.append(load_time)
        time.sleep(0.1)  # 避免请求过快
    
    if load_times:
        avg_load_time = sum(load_times) / len(load_times)
        print(f"\n📈 平均页面加载时间: {avg_load_time:.2f}ms")
        
        if avg_load_time < 100:
            print("🎉 性能优秀！")
        elif avg_load_time < 300:
            print("✅ 性能良好")
        elif avg_load_time < 1000:
            print("⚠️  性能一般，建议优化")
        else:
            print("❌ 性能较差，需要优化")
    
    print("\n🔄 并发性能测试:")
    print("-" * 40)
    
    # 测试并发性能
    test_concurrent_requests(session, f"{BASE_URL}/dashboard", 5)
    
    print("\n" + "=" * 60)
    print("✅ 性能测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
