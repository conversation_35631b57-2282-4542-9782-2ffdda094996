#!/usr/bin/env python3
"""
数据库修复脚本 - 添加缺失的字段
"""
import sqlite3
import os

def fix_database():
    """修复数据库，添加缺失的字段"""
    db_path = "paocai.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 检查数据库结构...")
        
        # 检查orders表结构
        cursor.execute("PRAGMA table_info(orders)")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"当前orders表字段: {columns}")
        
        # 添加缺失的字段
        if 'dining_end_time' not in columns:
            print("🔧 添加 dining_end_time 字段...")
            cursor.execute("ALTER TABLE orders ADD COLUMN dining_end_time DATETIME")
            print("✅ dining_end_time 字段添加成功")
        else:
            print("✅ dining_end_time 字段已存在")
        
        # 检查order_items表结构
        cursor.execute("PRAGMA table_info(order_items)")
        order_items_columns = [row[1] for row in cursor.fetchall()]
        print(f"当前order_items表字段: {order_items_columns}")
        
        # 添加order_items表的缺失字段
        if 'waiter_status' not in order_items_columns:
            print("🔧 添加 waiter_status 字段...")
            cursor.execute("ALTER TABLE order_items ADD COLUMN waiter_status VARCHAR(20)")
            print("✅ waiter_status 字段添加成功")
        
        if 'waiter_confirmed' not in order_items_columns:
            print("🔧 添加 waiter_confirmed 字段...")
            cursor.execute("ALTER TABLE order_items ADD COLUMN waiter_confirmed BOOLEAN DEFAULT 0")
            print("✅ waiter_confirmed 字段添加成功")
        
        if 'confirmed_at' not in order_items_columns:
            print("🔧 添加 confirmed_at 字段...")
            cursor.execute("ALTER TABLE order_items ADD COLUMN confirmed_at DATETIME")
            print("✅ confirmed_at 字段添加成功")
        
        # 检查是否存在必要的表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"当前数据库表: {tables}")
        
        # 创建command_templates表（如果不存在）
        if 'command_templates' not in tables:
            print("🔧 创建 command_templates 表...")
            cursor.execute("""
                CREATE TABLE command_templates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) NOT NULL,
                    code VARCHAR(50) NOT NULL UNIQUE,
                    description TEXT,
                    voice_text VARCHAR(200),
                    category VARCHAR(50) DEFAULT 'general',
                    is_active BOOLEAN DEFAULT 1,
                    sort_order INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print("✅ command_templates 表创建成功")
        
        # 创建waiter_actions表（如果不存在）
        if 'waiter_actions' not in tables:
            print("🔧 创建 waiter_actions 表...")
            cursor.execute("""
                CREATE TABLE waiter_actions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    waiter_id INTEGER NOT NULL,
                    room_number VARCHAR(20) NOT NULL,
                    action_type VARCHAR(50) NOT NULL,
                    action_content TEXT,
                    is_processed BOOLEAN DEFAULT 0,
                    processed_by INTEGER,
                    processed_at DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print("✅ waiter_actions 表创建成功")
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库修复失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 开始修复数据库...")
    if fix_database():
        print("🎉 数据库修复成功！")
    else:
        print("💥 数据库修复失败！")
