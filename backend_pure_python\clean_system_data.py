#!/usr/bin/env python3
"""
暨阳湖大酒店传菜管理系统 - 数据清理和状态重置脚本
"""
import sqlite3
import os
from datetime import datetime

def clean_database():
    """清理数据库中的异常数据"""
    print("🧹 开始清理数据库异常数据...")
    
    db_path = "paocai.db"
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 清理服务员-包厢指派关系
        print("📋 清理服务员-包厢指派关系...")
        cursor.execute("UPDATE tables SET assigned_waiter_id = NULL WHERE assigned_waiter_id IS NOT NULL")
        affected_tables = cursor.rowcount
        print(f"   清理了 {affected_tables} 个包厢的服务员指派")
        
        # 2. 重置包厢状态为可用
        print("🏠 重置包厢状态...")
        cursor.execute("UPDATE tables SET status = 'AVAILABLE' WHERE status != 'AVAILABLE'")
        affected_status = cursor.rowcount
        print(f"   重置了 {affected_status} 个包厢的状态")
        
        # 3. 清理未完成的订单
        print("📝 清理异常订单状态...")
        cursor.execute("""
            UPDATE orders 
            SET status = 'CANCELLED', 
                waiter_id = NULL,
                dining_start_time = NULL
            WHERE status IN ('PENDING_START', 'SERVING', 'IN_PROGRESS')
        """)
        affected_orders = cursor.rowcount
        print(f"   清理了 {affected_orders} 个异常订单")
        
        # 4. 清理订单项状态
        print("🍽️ 重置菜品状态...")
        cursor.execute("""
            UPDATE order_items 
            SET status = 'PENDING_COOK'
            WHERE status IN ('COOKING', 'READY', 'SERVED')
        """)
        affected_items = cursor.rowcount
        print(f"   重置了 {affected_items} 个菜品状态")
        
        # 5. 清理服务员授权状态
        print("👤 清理服务员授权状态...")
        cursor.execute("UPDATE users SET assigned_tables = NULL, is_authorized = 0 WHERE role = 'waiter'")
        affected_waiters = cursor.rowcount
        print(f"   清理了 {affected_waiters} 个服务员的授权状态")
        
        # 6. 清理服务员操作记录
        print("📋 清理服务员操作记录...")
        cursor.execute("DELETE FROM waiter_actions WHERE created_at < datetime('now', '-1 day')")
        affected_actions = cursor.rowcount
        print(f"   清理了 {affected_actions} 条历史操作记录")
        
        # 提交更改
        conn.commit()
        print("✅ 数据清理完成")
        
        # 验证清理结果
        print("\n📊 清理结果验证:")
        
        # 检查包厢状态
        cursor.execute("SELECT COUNT(*) FROM tables WHERE status = 'AVAILABLE'")
        available_tables = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM tables")
        total_tables = cursor.fetchone()[0]
        print(f"   包厢状态: {available_tables}/{total_tables} 个包厢可用")
        
        # 检查服务员授权
        cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'waiter' AND is_authorized = 0")
        unauthorized_waiters = cursor.fetchone()[0]
        print(f"   服务员授权: {unauthorized_waiters} 个服务员未授权（正常）")
        
        # 检查活跃订单
        cursor.execute("SELECT COUNT(*) FROM orders WHERE status IN ('PENDING_START', 'SERVING', 'IN_PROGRESS')")
        active_orders = cursor.fetchone()[0]
        print(f"   活跃订单: {active_orders} 个（应为0）")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据清理失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def check_data_consistency():
    """检查数据一致性"""
    print("\n🔍 检查数据一致性...")
    
    db_path = "paocai.db"
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        issues = []
        
        # 1. 检查包厢-服务员关联一致性
        cursor.execute("""
            SELECT t.number, t.assigned_waiter_id, u.username 
            FROM tables t 
            LEFT JOIN users u ON t.assigned_waiter_id = u.id 
            WHERE t.assigned_waiter_id IS NOT NULL
        """)
        table_assignments = cursor.fetchall()
        if table_assignments:
            issues.append(f"发现 {len(table_assignments)} 个包厢仍有服务员指派")
        
        # 2. 检查订单状态一致性
        cursor.execute("""
            SELECT COUNT(*) FROM orders 
            WHERE status IN ('PENDING_START', 'SERVING', 'IN_PROGRESS')
        """)
        active_orders = cursor.fetchone()[0]
        if active_orders > 0:
            issues.append(f"发现 {active_orders} 个活跃订单状态异常")
        
        # 3. 检查服务员授权状态
        cursor.execute("""
            SELECT COUNT(*) FROM users 
            WHERE role = 'waiter' AND (is_authorized = 1 OR assigned_tables IS NOT NULL)
        """)
        authorized_waiters = cursor.fetchone()[0]
        if authorized_waiters > 0:
            issues.append(f"发现 {authorized_waiters} 个服务员仍有授权状态")
        
        # 4. 检查菜品状态
        cursor.execute("""
            SELECT COUNT(*) FROM order_items 
            WHERE status IN ('COOKING', 'READY', 'SERVED')
        """)
        active_items = cursor.fetchone()[0]
        if active_items > 0:
            issues.append(f"发现 {active_items} 个菜品状态异常")
        
        conn.close()
        
        if issues:
            print("❌ 发现数据一致性问题:")
            for issue in issues:
                print(f"   • {issue}")
            return False
        else:
            print("✅ 数据一致性检查通过")
            return True
            
    except Exception as e:
        print(f"❌ 数据一致性检查失败: {e}")
        if 'conn' in locals():
            conn.close()
        return False

def reset_system_state():
    """重置系统状态"""
    print("\n🔄 重置系统状态...")
    
    # 清理可能的进程
    os.system("pkill -f 'main.py' 2>/dev/null")
    os.system("lsof -ti :8001 | xargs kill -9 2>/dev/null")
    
    print("✅ 系统进程已清理")
    return True

def main():
    """主函数"""
    print("🎯 暨阳湖大酒店传菜管理系统 - 数据清理和状态重置")
    print("=" * 60)
    
    # 1. 重置系统状态
    if not reset_system_state():
        print("❌ 系统状态重置失败")
        return False
    
    # 2. 清理数据库
    if not clean_database():
        print("❌ 数据库清理失败")
        return False
    
    # 3. 检查数据一致性
    if not check_data_consistency():
        print("❌ 数据一致性检查失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 系统清理完成！")
    print("\n📋 清理结果:")
    print("   • 所有包厢状态重置为可用")
    print("   • 服务员授权状态已清理")
    print("   • 异常订单状态已重置")
    print("   • 菜品状态已重置")
    print("   • 历史操作记录已清理")
    print("   • 数据一致性验证通过")
    print("\n🚀 系统已准备好重新启动！")
    
    return True

if __name__ == "__main__":
    main()
