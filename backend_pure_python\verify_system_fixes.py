#!/usr/bin/env python3
"""
验证暨阳湖大酒店传菜管理系统的功能完善和问题修复
"""
import os
import re
import requests
from pathlib import Path

def check_kitchen_display_title():
    """检查厨房大屏标题显示优化"""
    print("🖥️ 检查厨房大屏标题显示优化...")
    
    kitchen_template = "templates/kitchen_display_new.html"
    if not os.path.exists(kitchen_template):
        print("❌ 厨房大屏模板不存在")
        return False
        
    with open(kitchen_template, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查标题相关元素
    required_elements = [
        "header-title",  # 标题样式类
        "暨阳湖大酒店传菜管理系统",  # 标题文字
        "font-size: 1.3rem",  # 标题字体大小
        "color: #ffff00",  # 黄色标题
        "position: absolute",  # 绝对定位
        "left: 20px",  # 左边距
        "@media (max-width: 480px)",  # 小屏幕适配
        "display: none"  # 超小屏幕隐藏
    ]
    
    for element in required_elements:
        if element in content:
            print(f"✅ {element}")
        else:
            print(f"❌ 缺少元素: {element}")
            return False
    
    return True

def check_waiter_button_state_persistence():
    """检查服务员界面按钮状态持久化"""
    print("\n📱 检查服务员界面按钮状态持久化...")
    
    waiter_template = "templates/waiter_menu.html"
    if not os.path.exists(waiter_template):
        print("❌ 服务员界面模板不存在")
        return False
        
    with open(waiter_template, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查按钮状态初始化相关功能
    required_elements = [
        "initializeButtonStates",  # 初始化按钮状态函数
        "check-dining-status",  # 检查用餐状态API
        "enableCommandButtons",  # 启用按钮函数
        "disableCommandButtons",  # 禁用按钮函数
        "syncGuestCountToAllInterfaces",  # 人数同步函数
        "api/sync-guest-count"  # 人数同步API
    ]
    
    for element in required_elements:
        if element in content:
            print(f"✅ {element}")
        else:
            print(f"❌ 缺少元素: {element}")
            return False
    
    return True

def check_dish_progress_display():
    """检查菜品进度显示功能"""
    print("\n📊 检查菜品进度显示功能...")
    
    waiter_template = "templates/waiter_menu.html"
    waiter_css = "static/css/waiter-mobile.css"
    
    if not os.path.exists(waiter_template) or not os.path.exists(waiter_css):
        print("❌ 服务员界面文件不存在")
        return False
        
    with open(waiter_template, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    with open(waiter_css, 'r', encoding='utf-8') as f:
        css_content = f.read()
    
    # 检查进度显示相关元素
    template_elements = [
        "refresh-container",  # 刷新容器
        "dish-progress",  # 菜品进度显示
        "progress-text",  # 进度文字
        "remaining-text",  # 剩余文字
        "updateDishProgress",  # 更新进度函数
        "菜品进度：",  # 进度显示格式
        "还有0道菜未处理"  # 剩余菜品显示
    ]
    
    for element in template_elements:
        if element in template_content:
            print(f"✅ 模板包含: {element}")
        else:
            print(f"❌ 模板缺少: {element}")
            return False
    
    # 检查CSS样式
    css_elements = [
        "refresh-container",
        "dish-progress",
        "progress-text",
        "remaining-text",
        "rgba(0, 0, 0, 0.8)",  # 背景色
        "color: #ffc107"  # 黄色文字
    ]
    
    for element in css_elements:
        if element in css_content:
            print(f"✅ CSS包含: {element}")
        else:
            print(f"❌ CSS缺少: {element}")
            return False
    
    return True

def check_voice_settings_fixes():
    """检查语音播报设置修复"""
    print("\n🔊 检查语音播报设置修复...")
    
    voice_template = "templates/voice_settings.html"
    if not os.path.exists(voice_template):
        print("❌ 语音设置模板不存在")
        return False
        
    with open(voice_template, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查必需功能设置
    required_elements = [
        "系统必需功能",  # 必需功能说明
        "厨房制作完成播放为系统必需功能",  # 说明文字
        "voice_enabled: true",  # 强制启用
        "broadcastConfigUpdate",  # 配置更新广播
        "api/broadcast-voice-config-update",  # 广播API
        "配置将立即应用到厨房操作和厨房大屏"  # 成功提示
    ]
    
    for element in required_elements:
        if element in content:
            print(f"✅ {element}")
        else:
            print(f"❌ 缺少元素: {element}")
            return False
    
    return True

def check_kitchen_operation_permissions():
    """检查厨房操作权限控制"""
    print("\n🔧 检查厨房操作权限控制...")
    
    kitchen_template = "templates/kitchen.html"
    if not os.path.exists(kitchen_template):
        print("❌ 厨房操作模板不存在")
        return False
        
    with open(kitchen_template, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查用餐状态显示和权限控制
    required_elements = [
        "▶️ 用餐进行中",  # 用餐进行中状态
        "⏸️ 等待用餐开始",  # 等待用餐开始状态
        "alert-success",  # 成功状态样式
        "alert-danger",  # 危险状态样式
        "请等待服务员发送开始用餐指令",  # 提示文字
        "等待用餐开始",  # 按钮禁用文字
        "disabled"  # 禁用属性
    ]
    
    for element in required_elements:
        if element in content:
            print(f"✅ {element}")
        else:
            print(f"❌ 缺少元素: {element}")
            return False
    
    return True

def check_command_template_fixes():
    """检查指令管理功能修复"""
    print("\n⚙️ 检查指令管理功能修复...")
    
    command_template = "templates/command_templates.html"
    if not os.path.exists(command_template):
        print("❌ 指令管理模板不存在")
        return False
        
    with open(command_template, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查手动输入状态显示修复
    required_elements = [
        "✅ 允许",  # 允许手动输入显示
        "❌ 不允许",  # 不允许手动输入显示
        "bg-success",  # 成功状态样式
        "bg-danger",  # 危险状态样式
        "check-circle",  # 勾选图标
        "x-circle"  # 叉号图标
    ]
    
    for element in required_elements:
        if element in content:
            print(f"✅ {element}")
        else:
            print(f"❌ 缺少元素: {element}")
            return False
    
    return True

def check_backend_api_additions():
    """检查后端API新增功能"""
    print("\n🔗 检查后端API新增功能...")
    
    main_py_path = "main.py"
    if not os.path.exists(main_py_path):
        print("❌ 主程序文件不存在")
        return False
        
    with open(main_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查新增的API端点
    required_apis = [
        "check-dining-status",  # 检查用餐状态API
        "sync-guest-count",  # 同步人数API
        "broadcast-voice-config-update",  # 广播语音配置更新API
        "验证用餐状态",  # 用餐状态验证逻辑
        "dining_started",  # 用餐开始状态检查
        "websocket_manager.broadcast_to_all"  # WebSocket广播
    ]
    
    for api in required_apis:
        if api in content:
            print(f"✅ {api}")
        else:
            print(f"❌ 缺少API: {api}")
            return False
    
    return True

def check_server_response():
    """检查服务器响应"""
    print("\n🌐 检查服务器响应...")
    
    try:
        # 检查登录页面
        response = requests.get("http://localhost:8001/login", timeout=5)
        if response.status_code == 200:
            print("✅ 登录页面正常访问")
        else:
            print(f"❌ 登录页面访问失败: {response.status_code}")
            return False
        
        # 检查厨房大屏页面
        response = requests.get("http://localhost:8001/kitchen/display", timeout=5)
        if response.status_code in [200, 401]:  # 401是未登录，正常
            print("✅ 厨房大屏页面可访问")
        else:
            print(f"❌ 厨房大屏页面访问失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 服务器连接失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🎯 暨阳湖大酒店传菜管理系统功能完善和问题修复验证")
    print("=" * 80)
    
    checks = [
        ("厨房大屏标题显示优化", check_kitchen_display_title),
        ("服务员界面按钮状态持久化", check_waiter_button_state_persistence),
        ("菜品进度显示功能", check_dish_progress_display),
        ("语音播报设置修复", check_voice_settings_fixes),
        ("厨房操作权限控制", check_kitchen_operation_permissions),
        ("指令管理功能修复", check_command_template_fixes),
        ("后端API新增功能", check_backend_api_additions),
        ("服务器响应", check_server_response)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}检查:")
        try:
            if check_func():
                passed += 1
            else:
                print(f"❌ {check_name}检查失败")
        except Exception as e:
            print(f"❌ {check_name}检查出错: {e}")
    
    print("\n" + "=" * 80)
    print(f"🎉 验证完成: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("✅ 功能完善和问题修复全面完成！")
        print("\n🌟 修复亮点:")
        print("   • 厨房大屏左上角添加系统标题，支持响应式隐藏")
        print("   • 服务员界面按钮状态持久化，页面刷新后保持状态")
        print("   • 用餐人数全局同步，实时更新到所有相关界面")
        print("   • 菜品处理进度显示，实时计数和状态反馈")
        print("   • 语音播报设置实时生效，WebSocket广播配置更新")
        print("   • 厨房制作完成播放设为系统必需功能")
        print("   • 厨房操作权限控制，严格验证用餐状态")
        print("   • 指令管理手动输入状态显示修复")
        print("   • 后端API完善，支持状态检查和数据同步")
        return True
    else:
        print("❌ 功能完善和问题修复仍需完善")
        return False

if __name__ == "__main__":
    main()
