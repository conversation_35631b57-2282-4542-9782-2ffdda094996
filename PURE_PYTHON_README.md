# 🔥 暨阳湖大酒店传菜管理系统 - 纯Python版本

## 🎯 为什么选择纯Python版本？

根据您的需求，我们重构了系统为纯Python版本，具有以下优势：

### ✅ 简化部署
- **只需Python**: 无需安装Node.js、npm等前端工具
- **内置数据库**: 默认使用SQLite，无需配置PostgreSQL
- **一键启动**: 简单的脚本即可启动整个系统

### ✅ 完整功能
- **用户管理**: 多角色权限控制
- **餐桌管理**: 实时状态监控
- **菜单管理**: 完整的菜品信息管理
- **订单管理**: 从下单到完成的全流程
- **工作台**: 实时数据统计

### ✅ 现代化界面
- **Bootstrap 5**: 现代化响应式设计
- **移动端友好**: 适配手机、平板、PC
- **直观操作**: 简洁易用的界面设计

## 🚀 快速开始

### 1. 环境要求
- Python 3.9 或更高版本
- 无需其他依赖

### 2. 一键启动

**Linux/Mac:**
```bash
chmod +x start_pure_python.sh
./start_pure_python.sh
```

**Windows:**
```cmd
start_pure_python.bat
```

### 3. 手动启动

```bash
# 进入纯Python版本目录
cd backend_pure_python

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python init_db.py

# 启动服务
python main.py
```

### 4. 访问系统

打开浏览器访问: **http://localhost:8000**

## 🔑 默认登录账号

| 角色 | 用户名 | 密码 | 权限说明 |
|------|--------|------|----------|
| 系统管理员 | admin | admin123 | 所有功能权限 |
| 餐饮经理 | manager01 | manager123 | 管理端功能 |
| 服务员 | waiter01 | waiter123 | 点餐、服务功能 |
| 厨师长 | chef01 | chef123 | 厨房管理功能 |
| 厨师 | chef02 | chef123 | 菜品制作功能 |
| 打荷员 | kitchen01 | kitchen123 | 厨房辅助功能 |
| 收银员 | cashier01 | cashier123 | 收银结账功能 |

## 📱 系统功能

### 🏠 工作台
- 实时数据统计
- 快速操作入口
- 系统状态监控
- 角色定制化界面

### 🏨 餐桌管理
- 餐桌状态实时显示
- 开台、清台操作
- 餐桌信息维护
- 容量和设施管理

### 📖 菜单管理
- 菜品信息管理
- 分类和价格设置
- 库存状态控制
- 特色标签管理

### 🛒 订单管理
- 在线点餐下单
- 订单状态跟踪
- 菜品制作进度
- 结账和支付

### 👥 用户管理
- 员工账号管理
- 角色权限分配
- 部门组织架构
- 操作日志记录

## 🛠️ 技术架构

### 后端技术
- **FastAPI**: 现代化Python Web框架
- **Jinja2**: 服务端模板引擎
- **SQLAlchemy**: ORM数据库操作
- **SQLite**: 轻量级数据库（可切换PostgreSQL）
- **JWT**: 用户认证和授权
- **Pydantic**: 数据验证和序列化

### 前端技术
- **Bootstrap 5**: 响应式CSS框架
- **JavaScript**: 原生JS增强交互
- **HTML5**: 语义化标记
- **CSS3**: 现代化样式

### 数据库设计
- **用户表**: 员工信息和权限
- **餐桌表**: 餐桌信息和状态
- **菜品表**: 菜单和价格信息
- **订单表**: 订单和订单项
- **日志表**: 操作记录和审计

## 📁 目录结构

```
backend_pure_python/
├── core/                   # 核心配置
│   ├── config.py          # 应用配置
│   ├── database.py        # 数据库配置
│   └── security.py        # 安全认证
├── models/                 # 数据模型
│   ├── user.py            # 用户模型
│   ├── table.py           # 餐桌模型
│   ├── menu.py            # 菜单模型
│   └── order.py           # 订单模型
├── templates/              # HTML模板
│   ├── base.html          # 基础模板
│   ├── login.html         # 登录页面
│   ├── dashboard.html     # 工作台
│   ├── tables.html        # 餐桌管理
│   ├── menu.html          # 菜单管理
│   ├── orders.html        # 订单管理
│   └── create_order.html  # 创建订单
├── static/                 # 静态文件
│   └── uploads/           # 上传文件
├── main.py                 # 应用入口
├── init_db.py             # 数据库初始化
└── requirements.txt       # Python依赖
```

## 🔧 配置说明

### 数据库配置
默认使用SQLite数据库，文件位于 `paocai.db`。

如需使用PostgreSQL，修改 `core/config.py`:
```python
DATABASE_URL = "postgresql://用户名:密码@localhost:5432/数据库名"
```

### 应用配置
在 `core/config.py` 中可以配置：
- 数据库连接
- JWT密钥
- 文件上传路径
- 调试模式等

## 🧪 测试验证

运行测试脚本验证系统功能：
```bash
python test_pure_python.py
```

测试内容包括：
- 页面访问测试
- 用户登录测试
- 功能模块测试
- 不同角色权限测试

## 🚀 生产部署

### 1. 使用Gunicorn部署
```bash
pip install gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### 2. 使用Nginx反向代理
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    location /static/ {
        alias /path/to/backend_pure_python/static/;
    }
}
```

### 3. 使用Docker部署
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
RUN python init_db.py

EXPOSE 8000
CMD ["python", "main.py"]
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -tulpn | grep :8000
   # 修改端口
   # 在main.py中修改port参数
   ```

2. **Python版本过低**
   ```bash
   # 检查Python版本
   python3 --version
   # 需要3.9或更高版本
   ```

3. **依赖安装失败**
   ```bash
   # 升级pip
   pip install --upgrade pip
   # 使用国内镜像
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

4. **数据库初始化失败**
   ```bash
   # 删除数据库文件重新初始化
   rm paocai.db
   python init_db.py
   ```

## 📞 技术支持

如果遇到问题：
1. 查看控制台错误信息
2. 检查Python版本和依赖
3. 运行测试脚本验证
4. 查看日志文件

## 🎉 总结

纯Python版本的暨阳湖大酒店传菜管理系统为您提供了：

- ✅ **简单部署**: 只需Python环境
- ✅ **完整功能**: 包含所有核心业务功能
- ✅ **现代界面**: 响应式设计，支持多设备
- ✅ **易于维护**: 纯Python技术栈，便于开发和维护
- ✅ **生产就绪**: 支持多种部署方式

现在您可以轻松地部署和使用这个餐厅管理系统了！🎊
