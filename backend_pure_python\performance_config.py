"""
性能优化配置
"""

# 数据库连接池配置
DATABASE_POOL_CONFIG = {
    "pool_size": 10,
    "max_overflow": 20,
    "pool_timeout": 30,
    "pool_recycle": 3600,
    "pool_pre_ping": True
}

# 缓存配置
CACHE_CONFIG = {
    "enable_query_cache": True,
    "cache_timeout": 300,  # 5分钟
    "max_cache_size": 1000
}

# 分页配置
PAGINATION_CONFIG = {
    "default_page_size": 20,
    "max_page_size": 100
}

# 静态资源优化
STATIC_CONFIG = {
    "enable_gzip": True,
    "cache_max_age": 86400,  # 1天
    "enable_etag": True
}
