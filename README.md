# 暨阳湖大酒店传菜管理系统

## 📋 项目概述

暨阳湖大酒店传菜管理系统是一套现代化的餐厅管理解决方案，专为中高端酒店餐饮服务设计。系统采用纯Python后端架构，提供完整的从预订、点餐、厨房生产到上菜的全流程数字化管理。

### 🎯 核心目标
- **流程优化**: 优化餐厅服务全流程，提升运营效率
- **实时协作**: 实现商务中心、服务员、厨房的实时信息同步
- **智能管理**: 提供智能推荐、语音播报、自动化管理功能
- **用户体验**: 简化操作流程，提升员工工作效率和顾客满意度

## 🏗️ 系统架构

### 技术栈
- **后端框架**: FastAPI + SQLAlchemy + SQLite
- **前端技术**: HTML5 + CSS3 + JavaScript (原生)
- **实时通信**: WebSocket + 轮询机制
- **数据库**: SQLite (支持PostgreSQL)
- **部署方式**: 纯Python单体应用，支持Docker部署

### 架构特点
- **纯Python实现**: 无需复杂的前端构建工具
- **离线优先**: 所有资源本地化，支持完全离线运行
- **响应式设计**: 适配桌面端、平板和移动设备
- **模块化设计**: 清晰的模块划分，易于维护和扩展

## 👥 用户角色与权限

### 角色体系
| 角色 | 英文标识 | 权限范围 | 主要功能 |
|------|----------|----------|----------|
| 系统管理员 | ADMIN | 全部权限 | 系统配置、用户管理、数据维护、强制操作 |
| 餐饮经理 | MANAGER | 管理权限 | 订单监控、服务员授权、报表查看、人员管理 |
| 厨师长 | CHEF_MANAGER | 厨房管理权限 | 厨房显示、订单分配、菜品管理、制作监控 |
| 服务员 | WAITER | 服务权限 | 开台点餐、订单服务、状态更新、菜品确认 |
| 打荷员 | KITCHEN_HELPER | 厨房辅助权限 | 菜品准备、传菜协助、厨房显示 |
| 商务中心 | BUSINESS_CENTER | 预订权限 | 包厢预订、订单创建、客户管理 |

### 权限控制
- **基于角色的访问控制(RBAC)**: 每个角色拥有特定的权限集合
- **动态权限验证**: 实时验证用户操作权限
- **服务员授权机制**: 餐饮经理可动态授权服务员负责特定包厢

## 🔄 业务流程

### 完整服务流程
```mermaid
graph TD
    A[商务中心预订] --> B[餐饮经理授权服务员]
    B --> C[服务员开始用餐]
    C --> D[厨房接收订单]
    D --> E[厨房制作菜品]
    E --> F[打荷员确认完成]
    F --> G[服务员上菜确认]
    G --> H[用餐完成结账]
```

### 关键业务节点

#### 1. 预订管理
- **商务中心创建订单**: 录入客户信息、用餐人数、菜单内容
- **包厢状态管理**: 自动更新包厢状态为"已预订"
- **重复检查**: 防止同一包厢重复预订

#### 2. 服务员授权
- **经理授权**: 餐饮经理为服务员分配负责的包厢
- **动态授权**: 支持实时更改服务员分配
- **权限验证**: 服务员只能操作被授权的包厢

#### 3. 用餐流程控制
- **开始用餐**: 服务员必须点击"开始用餐"才能进行其他操作
- **厨房通知**: 开始用餐时自动通知厨房大屏和打荷
- **状态同步**: 实时更新包厢和订单状态

#### 4. 厨房生产管理
- **实时显示**: 厨房大屏显示所有进行中的订单
- **菜品状态**: 待制作 → 制作中 → 已完成
- **语音播报**: 重要操作自动语音提醒

#### 5. 传菜协调
- **打荷确认**: 打荷员确认菜品完成，通知厨房大屏
- **服务员确认**: 服务员确认菜品已上桌
- **状态追踪**: 全程追踪菜品流转状态

## 📱 功能模块详解

### 1. 用户管理模块
- **用户创建**: 支持批量创建不同角色用户
- **权限管理**: 基于角色的细粒度权限控制
- **状态管理**: 用户激活/停用、授权状态管理
- **安全认证**: JWT令牌认证，会话管理

### 2. 包厢管理模块
- **包厢信息**: 包厢号、名称、容量、类型管理
- **状态追踪**: 可用、已预订、用餐中、清理中等状态
- **服务员分配**: 动态分配服务员负责包厢
- **使用统计**: 包厢使用率、翻台率统计

### 3. 订单管理模块
- **订单创建**: 支持文本菜单录入，自动解析菜品
- **状态流转**: 已预订 → 用餐中 → 已完成 → 已结账
- **菜品管理**: 单个菜品状态独立管理
- **修改记录**: 完整的订单修改历史记录

### 4. 厨房显示模块
- **实时显示**: 显示所有进行中的订单和菜品
- **分页显示**: 支持多页显示，自动轮播
- **状态过滤**: 只显示已开始用餐的包厢
- **视觉优化**: 黑色背景，高对比度显示

### 5. 打荷操作模块
- **菜品确认**: 确认菜品制作完成
- **服务员指令**: 接收和处理服务员指令
- **状态更新**: 实时更新菜品和订单状态
- **通知机制**: 完成时通知厨房大屏

### 6. 服务员界面模块
- **移动优化**: 专为移动设备优化的界面
- **权限控制**: 只显示被授权的包厢
- **操作简化**: 大按钮设计，操作简单直观
- **实时同步**: 与厨房、打荷实时同步

### 7. 系统配置模块
- **界面配置**: 主题切换、字体大小、布局调整
- **语音配置**: 语音播报开关、音量、语速设置
- **显示配置**: 厨房大屏显示参数配置
- **业务配置**: 超时设置、自动化规则配置

## 🔧 技术实现

### 后端架构
```
backend_pure_python/
├── main.py                 # 应用入口，路由定义
├── core/                   # 核心配置
│   ├── config.py          # 应用配置
│   ├── database.py        # 数据库配置
│   └── security.py        # 安全认证
├── models/                 # 数据模型
│   ├── user.py            # 用户模型
│   ├── table.py           # 包厢模型
│   ├── order.py           # 订单模型
│   ├── menu.py            # 菜单模型
│   └── waiter_action.py   # 服务员操作模型
├── templates/              # HTML模板
└── static/                # 静态资源
```

### 数据库设计
- **用户表(users)**: 用户信息、角色、权限、状态
- **包厢表(tables)**: 包厢信息、状态、容量、分配
- **订单表(orders)**: 订单信息、状态、金额、时间
- **订单项表(order_items)**: 菜品信息、状态、数量
- **服务员操作表(waiter_actions)**: 操作记录、指令、状态
- **系统配置表(system_configs)**: 系统参数配置
- **语音配置表(voice_configs)**: 语音播报配置

### 实时通信机制
- **WebSocket连接**: 用于实时状态更新
- **轮询机制**: 作为WebSocket的备用方案
- **事件驱动**: 基于事件的状态变更通知
- **消息队列**: 确保消息可靠传递

### 安全机制
- **JWT认证**: 基于令牌的身份验证
- **CSRF保护**: 防止跨站请求伪造
- **权限验证**: 每个操作都进行权限检查
- **会话管理**: 安全的会话生命周期管理

## 🚀 部署与运行

### 环境要求
- Python 3.10+
- SQLite 3.x (或PostgreSQL)
- 现代浏览器支持

### 快速启动
```bash
# 1. 克隆项目
git clone <repository-url>
cd paocai/backend_pure_python

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 初始化数据库
python init_db.py

# 5. 启动服务
python main.py
```

### 访问地址
- **主系统**: http://localhost:8001
- **API文档**: http://localhost:8001/docs
- **厨房大屏**: http://localhost:8001/kitchen/display
- **打荷操作**: http://localhost:8001/kitchen-helper

### 默认账号
- **管理员**: admin / admin123
- **餐饮经理**: manager01 / manager123
- **厨师长**: chef01 / chef123
- **服务员**: waiter01 / waiter123
- **商务中心**: business01 / business123

## 📊 系统特色

### 1. 离线优先设计
- **本地资源**: 所有CSS、JS、字体文件本地化
- **离线运行**: 无需互联网连接即可正常使用
- **数据同步**: 支持离线数据缓存和同步

### 2. 移动端优化
- **响应式布局**: 自适应不同屏幕尺寸
- **触控优化**: 大按钮设计，适合触屏操作
- **性能优化**: 轻量级实现，快速响应

### 3. 实时协作
- **状态同步**: 多端实时状态同步
- **消息通知**: 重要操作即时通知相关人员
- **冲突处理**: 智能处理并发操作冲突

### 4. 智能化功能
- **自动超时**: 包厢使用超时自动处理
- **语音播报**: 重要操作语音提醒
- **智能推荐**: 基于历史数据的智能建议

## 🔍 监控与维护

### 系统监控
- **性能监控**: 响应时间、并发数、资源使用率
- **错误监控**: 异常日志、错误统计、告警机制
- **业务监控**: 订单量、翻台率、服务效率

### 数据备份
- **自动备份**: 定期自动备份数据库
- **增量备份**: 支持增量备份策略
- **恢复机制**: 快速数据恢复功能

### 日志管理
- **操作日志**: 记录所有用户操作
- **系统日志**: 记录系统运行状态
- **审计日志**: 关键操作审计追踪

## 📈 扩展性

### 功能扩展
- **模块化设计**: 易于添加新功能模块
- **插件机制**: 支持第三方插件集成
- **API开放**: 提供完整的REST API

### 性能扩展
- **数据库优化**: 支持读写分离、分库分表
- **缓存机制**: Redis缓存支持
- **负载均衡**: 支持多实例部署

### 集成扩展
- **支付系统**: 支持多种支付方式集成
- **会员系统**: 客户管理和会员体系
- **财务系统**: 财务数据对接

## 🛠️ 开发指南

### 开发环境搭建
```bash
# 1. 克隆项目
git clone <repository-url>
cd paocai/backend_pure_python

# 2. 创建开发环境
python -m venv venv
source venv/bin/activate

# 3. 安装开发依赖
pip install -r requirements.txt

# 4. 配置开发数据库
python init_db.py

# 5. 启动开发服务器
python main.py
```

### 代码规范
- **Python代码**: 遵循PEP 8规范
- **HTML/CSS**: 使用语义化标签，响应式设计
- **JavaScript**: ES6+语法，模块化开发
- **数据库**: 规范化设计，索引优化

### 测试策略
- **单元测试**: 核心业务逻辑测试
- **集成测试**: API接口测试
- **端到端测试**: 完整业务流程测试
- **性能测试**: 并发和压力测试

## 📞 技术支持

### 问题反馈
- **GitHub Issues**: 提交bug报告和功能请求
- **技术文档**: 详细的API文档和开发指南
- **社区支持**: 开发者社区交流

### 版本更新
- **定期更新**: 持续功能改进和bug修复
- **版本兼容**: 向后兼容的升级策略
- **迁移指南**: 详细的版本升级指南

## 🔄 系统运行机制

### 启动流程
1. **数据库初始化**: 创建表结构，插入初始数据
2. **权限系统加载**: 加载用户角色和权限配置
3. **WebSocket服务启动**: 建立实时通信连接
4. **定时任务启动**: 包厢超时检查、数据清理等
5. **HTTP服务启动**: 提供Web界面和API服务

### 数据流转
1. **商务中心** → 创建订单 → **数据库**
2. **餐饮经理** → 授权服务员 → **权限更新**
3. **服务员** → 开始用餐 → **状态同步** → **厨房大屏**
4. **厨房** → 制作菜品 → **状态更新** → **打荷显示**
5. **打荷员** → 确认完成 → **通知推送** → **服务员端**

### 状态管理
- **包厢状态**: 可用 → 已预订 → 用餐中 → 清理中 → 可用
- **订单状态**: 已预订 → 用餐中 → 已完成 → 已结账
- **菜品状态**: 待制作 → 制作中 → 已完成 → 已上桌

## 🎯 最佳实践

### 部署建议
- **生产环境**: 使用PostgreSQL替代SQLite
- **负载均衡**: 使用Nginx进行反向代理
- **监控告警**: 配置系统监控和日志收集
- **数据备份**: 定期备份数据库和配置文件

### 性能优化
- **数据库索引**: 为常用查询字段添加索引
- **缓存策略**: 使用Redis缓存热点数据
- **静态资源**: 使用CDN加速静态文件访问
- **代码优化**: 定期进行代码审查和性能分析

### 安全建议
- **HTTPS部署**: 生产环境必须使用HTTPS
- **定期更新**: 及时更新依赖包和安全补丁
- **访问控制**: 配置防火墙和访问白名单
- **数据加密**: 敏感数据加密存储

---

**暨阳湖大酒店传菜管理系统** - 让餐厅管理更智能、更高效！

## 📋 附录

### API文档
系统提供完整的REST API文档，访问 `/docs` 查看详细的API接口说明。

### 数据库结构
详细的数据库表结构和关系图请参考 `docs/database.md` 文档。

### 配置说明
系统配置参数说明请参考 `docs/configuration.md` 文档。

### 故障排除
常见问题和解决方案请参考 `docs/troubleshooting.md` 文档。
