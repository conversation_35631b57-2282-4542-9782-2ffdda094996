#!/usr/bin/env python3
"""
菜品状态重置脚本
将所有菜品状态重置为简化的状态系统
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from sqlalchemy import text
from core.database import get_db
from models.order import OrderItem

def reset_dish_status():
    """重置所有菜品状态"""
    db = next(get_db())

    try:
        print("🔄 开始重置菜品状态...")

        # 使用原生SQL直接更新，避免枚举验证问题
        reset_count = 0

        # 将所有非cancelled状态的菜品重置为pending_cook
        result = db.execute(text("""
            UPDATE order_items
            SET status = 'pending_cook',
                started_cooking_at = NULL,
                ready_at = NULL,
                served_at = NULL,
                confirmed_at = NULL
            WHERE status != 'cancelled'
        """))

        reset_count = result.rowcount

        # 提交更改
        db.commit()
        print(f"✅ 菜品状态重置完成！共重置了 {reset_count} 个菜品项")

    except Exception as e:
        db.rollback()
        print(f"❌ 重置失败: {e}")
        raise
    finally:
        db.close()

def show_status_summary():
    """显示当前状态统计"""
    db = next(get_db())

    try:
        print("\n📊 当前菜品状态统计:")

        # 使用原生SQL查询状态统计
        result = db.execute(text("""
            SELECT status, COUNT(*) as count
            FROM order_items
            GROUP BY status
        """))

        total = 0
        for row in result:
            status, count = row
            print(f"  {status}: {count} 个")
            total += count

        print(f"  总计: {total} 个菜品项")

    except Exception as e:
        print(f"❌ 获取统计失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("🏨 暨阳湖大酒店传菜管理系统 - 菜品状态重置工具")
    print("=" * 50)
    
    # 显示重置前状态
    print("重置前状态:")
    show_status_summary()
    
    # 确认重置
    confirm = input("\n⚠️ 确定要重置所有菜品状态吗？(y/N): ")
    if confirm.lower() in ['y', 'yes']:
        reset_dish_status()
        
        # 显示重置后状态
        print("\n重置后状态:")
        show_status_summary()
    else:
        print("❌ 操作已取消")
