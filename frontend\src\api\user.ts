import { api } from './client';
import { User, UserRole, UserStatus, PaginatedResponse } from '@/types';

export interface UserCreateData {
  username: string;
  password: string;
  full_name: string;
  role: UserRole;
  email?: string;
  phone?: string;
  employee_id?: string;
  department?: string;
  position?: string;
}

export interface UserUpdateData {
  full_name?: string;
  email?: string;
  phone?: string;
  role?: UserRole;
  status?: UserStatus;
  employee_id?: string;
  department?: string;
  position?: string;
  assigned_areas?: string;
  is_active?: boolean;
}

export const userApi = {
  // 获取用户列表
  getUsers: (params?: {
    page?: number;
    size?: number;
    search?: string;
    role?: UserRole;
    status?: UserStatus;
    department?: string;
  }): Promise<PaginatedResponse<User>> => {
    return api.get('/api/users', { params });
  },
  
  // 创建用户
  createUser: (data: UserCreateData): Promise<User> => {
    return api.post('/api/users', data);
  },
  
  // 获取用户详情
  getUser: (id: number): Promise<User> => {
    return api.get(`/api/users/${id}`);
  },
  
  // 更新用户信息
  updateUser: (id: number, data: UserUpdateData): Promise<User> => {
    return api.put(`/api/users/${id}`, data);
  },
  
  // 删除用户
  deleteUser: (id: number): Promise<{ message: string }> => {
    return api.delete(`/api/users/${id}`);
  },
  
  // 重置用户密码
  resetUserPassword: (id: number, newPassword: string): Promise<{ message: string }> => {
    return api.post(`/api/users/${id}/reset-password`, { new_password: newPassword });
  },
  
  // 获取用户角色列表
  getUserRoles: (): Promise<{ roles: Array<{ value: string; label: string }> }> => {
    return api.get('/api/users/roles/list');
  },
};
