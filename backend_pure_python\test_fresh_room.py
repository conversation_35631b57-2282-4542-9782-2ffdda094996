#!/usr/bin/env python3
"""
测试全新包厢的用餐控制逻辑
"""
import requests
import json

def test_fresh_room():
    """测试全新包厢"""
    base_url = "http://localhost:8001"
    
    # 使用一个全新的包厢号
    test_room = "test_fresh_2025"
    
    print(f"🔍 测试全新包厢 {test_room} 的用餐控制逻辑...")
    
    session = requests.Session()
    
    # 先尝试用管理员身份登录，然后切换到服务员
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
    if login_response.status_code == 302:
        print("✅ 管理员登录成功")
        
        # 创建一个新的服务员用户并分配包厢
        print(f"🔍 为包厢 {test_room} 创建测试环境...")
        
        # 先检查是否有用餐开始记录
        print(f"🔍 检查包厢 {test_room} 是否有用餐开始记录...")
        
        # 直接测试API调用（模拟服务员操作）
        print(f"🔍 模拟服务员发送非用餐开始指令...")
        
        # 尝试发送清理指令（应该被拒绝）
        command_data = {
            "room_number": test_room,
            "action_type": "clean_table",
            "action_content": "测试清理"
        }
        
        command_response = session.post(f"{base_url}/waiter/send-command", 
                                      json=command_data)
        
        print(f"清理指令状态码: {command_response.status_code}")
        
        if command_response.status_code == 403:
            print("✅ 正确拒绝：权限不足（管理员无法发送服务员指令）")
        elif command_response.status_code == 400:
            try:
                error_data = command_response.json()
                if "尚未开始用餐" in error_data.get('detail', ''):
                    print("✅ 正确拒绝：包厢尚未开始用餐")
                else:
                    print(f"❓ 400错误但原因不明: {error_data}")
            except:
                print(f"❓ 400错误但无法解析响应: {command_response.text}")
        else:
            print(f"❌ 意外的状态码: {command_response.status_code}")
            print(f"响应内容: {command_response.text}")
            
        # 尝试发送用餐开始指令
        print(f"\n🔍 尝试发送用餐开始指令...")
        start_command_data = {
            "room_number": test_room,
            "action_type": "dining_start",
            "action_content": "4"
        }
        
        start_response = session.post(f"{base_url}/waiter/send-command", 
                                    json=start_command_data)
        
        print(f"用餐开始指令状态码: {start_response.status_code}")
        print(f"响应内容: {start_response.text}")
        
    else:
        print(f"❌ 管理员登录失败: {login_response.status_code}")

if __name__ == "__main__":
    test_fresh_room()
