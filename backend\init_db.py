#!/usr/bin/env python3
"""
数据库初始化脚本
创建初始数据，包括管理员用户、示例菜品、餐桌等
"""

from sqlalchemy.orm import Session
from core.database import SessionLocal, engine, Base
from core.security import get_password_hash
from models.user import User, UserRole, UserStatus
from models.table import Table, TableType, TableStatus
from models.menu import Dish, DishCategory, DishStatus, SpicyLevel, CookingMethod
from decimal import Decimal


def create_admin_user(db: Session):
    """创建管理员用户"""
    admin = db.query(User).filter(User.username == "admin").first()
    if not admin:
        admin = User(
            username="admin",
            hashed_password=get_password_hash("admin123"),
            full_name="系统管理员",
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            employee_id="ADMIN001",
            department="管理部",
            position="系统管理员",
            is_superuser=True
        )
        db.add(admin)
        print("✓ 创建管理员用户: admin/admin123")
    
    # 创建示例用户
    users_data = [
        {
            "username": "manager01",
            "password": "manager123",
            "full_name": "张经理",
            "role": UserRole.MANAGER,
            "employee_id": "MGR001",
            "department": "餐饮部",
            "position": "餐饮经理"
        },
        {
            "username": "waiter01",
            "password": "waiter123",
            "full_name": "李小美",
            "role": UserRole.WAITER,
            "employee_id": "WTR001",
            "department": "餐饮部",
            "position": "服务员"
        },
        {
            "username": "chef01",
            "password": "chef123",
            "full_name": "王师傅",
            "role": UserRole.CHEF_MANAGER,
            "employee_id": "CHF001",
            "department": "厨房",
            "position": "厨师长"
        },
        {
            "username": "kitchen01",
            "password": "kitchen123",
            "full_name": "刘打荷",
            "role": UserRole.KITCHEN_HELPER,
            "employee_id": "KTH001",
            "department": "厨房",
            "position": "打荷员"
        }
    ]
    
    for user_data in users_data:
        existing_user = db.query(User).filter(User.username == user_data["username"]).first()
        if not existing_user:
            user = User(
                username=user_data["username"],
                hashed_password=get_password_hash(user_data["password"]),
                full_name=user_data["full_name"],
                role=user_data["role"],
                status=UserStatus.ACTIVE,
                employee_id=user_data["employee_id"],
                department=user_data["department"],
                position=user_data["position"]
            )
            db.add(user)
            print(f"✓ 创建用户: {user_data['username']}/{user_data['password']}")


def create_sample_tables(db: Session):
    """创建示例餐桌"""
    tables_data = [
        # 大厅餐桌
        {"number": "A01", "name": "大厅1号桌", "type": TableType.HALL_TABLE, "capacity": 4, "floor": "1F", "area": "大厅A区"},
        {"number": "A02", "name": "大厅2号桌", "type": TableType.HALL_TABLE, "capacity": 6, "floor": "1F", "area": "大厅A区"},
        {"number": "A03", "name": "大厅3号桌", "type": TableType.HALL_TABLE, "capacity": 8, "floor": "1F", "area": "大厅A区"},
        {"number": "B01", "name": "大厅4号桌", "type": TableType.HALL_TABLE, "capacity": 4, "floor": "1F", "area": "大厅B区"},
        {"number": "B02", "name": "大厅5号桌", "type": TableType.HALL_TABLE, "capacity": 6, "floor": "1F", "area": "大厅B区"},
        
        # 包厢
        {"number": "VIP01", "name": "暨阳厅", "type": TableType.VIP_ROOM, "capacity": 12, "floor": "2F", "area": "VIP区", 
         "has_tv": True, "has_karaoke": True, "minimum_charge": Decimal("800")},
        {"number": "VIP02", "name": "湖景厅", "type": TableType.VIP_ROOM, "capacity": 16, "floor": "2F", "area": "VIP区", 
         "has_tv": True, "has_karaoke": True, "has_projector": True, "minimum_charge": Decimal("1200")},
        {"number": "PRI01", "name": "雅间1", "type": TableType.PRIVATE_ROOM, "capacity": 8, "floor": "2F", "area": "包厢区", 
         "has_tv": True, "minimum_charge": Decimal("400")},
        {"number": "PRI02", "name": "雅间2", "type": TableType.PRIVATE_ROOM, "capacity": 10, "floor": "2F", "area": "包厢区", 
         "has_tv": True, "minimum_charge": Decimal("500")},
        {"number": "PRI03", "name": "雅间3", "type": TableType.PRIVATE_ROOM, "capacity": 6, "floor": "2F", "area": "包厢区", 
         "has_tv": True, "minimum_charge": Decimal("300")},
    ]
    
    for table_data in tables_data:
        existing_table = db.query(Table).filter(Table.number == table_data["number"]).first()
        if not existing_table:
            table = Table(
                number=table_data["number"],
                name=table_data["name"],
                table_type=table_data["type"],
                capacity=table_data["capacity"],
                floor=table_data.get("floor"),
                area=table_data.get("area"),
                has_tv=table_data.get("has_tv", False),
                has_karaoke=table_data.get("has_karaoke", False),
                has_projector=table_data.get("has_projector", False),
                has_wifi=True,
                has_air_conditioning=True,
                minimum_charge=table_data.get("minimum_charge"),
                service_charge_rate=Decimal("10") if table_data["type"] in [TableType.VIP_ROOM, TableType.PRIVATE_ROOM] else None
            )
            db.add(table)
            print(f"✓ 创建餐桌: {table_data['number']} - {table_data['name']}")


def create_sample_dishes(db: Session):
    """创建示例菜品"""
    dishes_data = [
        # 招牌菜
        {
            "name": "暨阳湖大闸蟹",
            "category": DishCategory.SEAFOOD,
            "price": Decimal("88.00"),
            "cost": Decimal("45.00"),
            "description": "暨阳湖特产大闸蟹，肉质鲜美，蟹黄饱满",
            "ingredients": "大闸蟹、生姜、黄酒",
            "spicy_level": SpicyLevel.NONE,
            "cooking_method": CookingMethod.STEAM,
            "prep_time": 10,
            "cook_time": 15,
            "is_signature": True,
            "is_recommended": True,
            "kitchen_station": "海鲜档"
        },
        {
            "name": "红烧狮子头",
            "category": DishCategory.HOT_DISH,
            "price": Decimal("38.00"),
            "cost": Decimal("18.00"),
            "description": "传统江南名菜，肉质鲜嫩，汤汁浓郁",
            "ingredients": "猪肉糜、马蹄、鸡蛋、生抽",
            "spicy_level": SpicyLevel.NONE,
            "cooking_method": CookingMethod.BRAISE,
            "prep_time": 20,
            "cook_time": 30,
            "is_signature": True,
            "kitchen_station": "热菜档"
        },
        
        # 热菜
        {
            "name": "宫保鸡丁",
            "category": DishCategory.HOT_DISH,
            "price": Decimal("28.00"),
            "cost": Decimal("12.00"),
            "description": "经典川菜，鸡肉嫩滑，花生香脆",
            "ingredients": "鸡胸肉、花生米、干辣椒、花椒",
            "spicy_level": SpicyLevel.MEDIUM,
            "cooking_method": CookingMethod.STIR_FRY,
            "prep_time": 15,
            "cook_time": 8,
            "is_popular": True,
            "kitchen_station": "热菜档"
        },
        {
            "name": "糖醋里脊",
            "category": DishCategory.HOT_DISH,
            "price": Decimal("32.00"),
            "cost": Decimal("15.00"),
            "description": "酸甜可口，外酥内嫩",
            "ingredients": "猪里脊肉、番茄酱、白醋、白糖",
            "spicy_level": SpicyLevel.NONE,
            "cooking_method": CookingMethod.DEEP_FRY,
            "prep_time": 20,
            "cook_time": 12,
            "kitchen_station": "热菜档"
        },
        {
            "name": "麻婆豆腐",
            "category": DishCategory.HOT_DISH,
            "price": Decimal("18.00"),
            "cost": Decimal("8.00"),
            "description": "经典川菜，麻辣鲜香",
            "ingredients": "嫩豆腐、牛肉糜、豆瓣酱、花椒",
            "spicy_level": SpicyLevel.HOT,
            "cooking_method": CookingMethod.STIR_FRY,
            "prep_time": 10,
            "cook_time": 8,
            "kitchen_station": "热菜档"
        },
        
        # 凉菜
        {
            "name": "口水鸡",
            "category": DishCategory.COLD_DISH,
            "price": Decimal("25.00"),
            "cost": Decimal("12.00"),
            "description": "四川名菜，麻辣鲜香，口感嫩滑",
            "ingredients": "鸡腿肉、花椒、辣椒油、蒜泥",
            "spicy_level": SpicyLevel.HOT,
            "cooking_method": CookingMethod.COLD_MIX,
            "prep_time": 30,
            "cook_time": 20,
            "kitchen_station": "凉菜档"
        },
        {
            "name": "拍黄瓜",
            "category": DishCategory.COLD_DISH,
            "price": Decimal("12.00"),
            "cost": Decimal("5.00"),
            "description": "清爽开胃，简单美味",
            "ingredients": "黄瓜、蒜泥、香醋、香油",
            "spicy_level": SpicyLevel.MILD,
            "cooking_method": CookingMethod.COLD_MIX,
            "prep_time": 10,
            "cook_time": 0,
            "kitchen_station": "凉菜档"
        },
        
        # 汤羹
        {
            "name": "西湖牛肉羹",
            "category": DishCategory.SOUP,
            "price": Decimal("22.00"),
            "cost": Decimal("10.00"),
            "description": "杭州名菜，汤色清澈，味道鲜美",
            "ingredients": "牛肉丝、鸡蛋清、香菇、笋丝",
            "spicy_level": SpicyLevel.NONE,
            "cooking_method": CookingMethod.BOIL,
            "prep_time": 15,
            "cook_time": 10,
            "kitchen_station": "汤档"
        },
        {
            "name": "紫菜蛋花汤",
            "category": DishCategory.SOUP,
            "price": Decimal("15.00"),
            "cost": Decimal("6.00"),
            "description": "家常汤品，营养丰富",
            "ingredients": "紫菜、鸡蛋、香葱、香油",
            "spicy_level": SpicyLevel.NONE,
            "cooking_method": CookingMethod.BOIL,
            "prep_time": 5,
            "cook_time": 5,
            "kitchen_station": "汤档"
        },
        
        # 主食
        {
            "name": "扬州炒饭",
            "category": DishCategory.STAPLE,
            "price": Decimal("20.00"),
            "cost": Decimal("8.00"),
            "description": "经典炒饭，粒粒分明，配菜丰富",
            "ingredients": "米饭、鸡蛋、火腿、虾仁、青豆",
            "spicy_level": SpicyLevel.NONE,
            "cooking_method": CookingMethod.STIR_FRY,
            "prep_time": 10,
            "cook_time": 8,
            "kitchen_station": "面点档"
        },
        {
            "name": "小笼包",
            "category": DishCategory.STAPLE,
            "price": Decimal("16.00"),
            "cost": Decimal("7.00"),
            "description": "江南特色，皮薄馅大，汤汁鲜美",
            "ingredients": "面粉、猪肉糜、高汤、韭黄",
            "spicy_level": SpicyLevel.NONE,
            "cooking_method": CookingMethod.STEAM,
            "prep_time": 30,
            "cook_time": 15,
            "unit": "笼",
            "serving_size": "8个/笼",
            "kitchen_station": "面点档"
        },
        
        # 饮品
        {
            "name": "鲜榨橙汁",
            "category": DishCategory.BEVERAGE,
            "price": Decimal("18.00"),
            "cost": Decimal("8.00"),
            "description": "新鲜橙子现榨，维C丰富",
            "ingredients": "新鲜橙子",
            "spicy_level": SpicyLevel.NONE,
            "prep_time": 5,
            "cook_time": 0,
            "kitchen_station": "饮品档"
        },
        {
            "name": "柠檬蜂蜜茶",
            "category": DishCategory.BEVERAGE,
            "price": Decimal("15.00"),
            "cost": Decimal("6.00"),
            "description": "酸甜清香，生津止渴",
            "ingredients": "柠檬、蜂蜜、绿茶",
            "spicy_level": SpicyLevel.NONE,
            "prep_time": 3,
            "cook_time": 0,
            "kitchen_station": "饮品档"
        }
    ]
    
    for dish_data in dishes_data:
        existing_dish = db.query(Dish).filter(Dish.name == dish_data["name"]).first()
        if not existing_dish:
            dish = Dish(**dish_data)
            db.add(dish)
            print(f"✓ 创建菜品: {dish_data['name']} - ¥{dish_data['price']}")


def init_database():
    """初始化数据库"""
    print("开始初始化数据库...")
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    print("✓ 数据库表创建完成")
    
    # 创建数据库会话
    db = SessionLocal()
    
    try:
        # 创建初始数据
        create_admin_user(db)
        create_sample_tables(db)
        create_sample_dishes(db)
        
        # 提交事务
        db.commit()
        print("✓ 初始数据创建完成")
        
        print("\n" + "="*50)
        print("数据库初始化完成！")
        print("="*50)
        print("默认管理员账号:")
        print("  用户名: admin")
        print("  密码: admin123")
        print("\n其他测试账号:")
        print("  经理: manager01/manager123")
        print("  服务员: waiter01/waiter123")
        print("  厨师长: chef01/chef123")
        print("  打荷员: kitchen01/kitchen123")
        print("="*50)
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    init_database()
