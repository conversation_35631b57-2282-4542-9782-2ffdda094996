{% extends "base.html" %}

{% block title %}订单管理 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h1 class="h3 mb-0">订单管理</h1>
                <button type="button" class="btn btn-refresh btn-sm" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise"></i>
                    刷新
                </button>
            </div>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-12">
            <div class="btn-group me-2">
                {% if user.is_service_staff %}
                <a href="/orders/create" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus"></i>
                    新建订单
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">订单列表</h5>
                </div>
                <div class="card-body">
                    {% if orders %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>包厢</th>
                                    <th>客户信息</th>
                                    <th>用餐标准</th>
                                    <th>状态</th>
                                    <th>用餐时段</th>
                                    <th>服务员</th>
                                    <th>创建时间</th>
                                    <th>用餐结束时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr onclick="showOrderDetails({{ order.id }})" style="cursor: pointer;">
                                    <td>{{ order.table.number if order.table else '无' }}</td>
                                    <td>
                                        <div>{{ order.customer_name or '无' }}</div>
                                        <small class="text-muted">{{ order.customer_phone or '无' }}</small>
                                    </td>
                                    <td>¥{{ order.dining_standard_amount or order.total_amount or 0 }}/桌</td>
                                    <td>
                                        {% if order.status == 'reserved' %}
                                            <span class="badge bg-info">已预订</span>
                                        {% elif order.status == 'pending_start' %}
                                            <span class="badge bg-warning">待开始</span>
                                        {% elif order.status == 'serving' %}
                                            <span class="badge bg-primary">已上菜</span>
                                            {% if order.started_at %}
                                            <br><small class="text-muted">{{ order.started_at.strftime('%H:%M') }}</small>
                                            {% endif %}
                                        {% elif order.status == 'completed' %}
                                            <span class="badge bg-success">已完成</span>
                                        {% elif order.status == 'cancelled' %}
                                            <span class="badge bg-danger">已取消</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ order.get_status_display() }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if order.meal_period == 'breakfast' %}
                                            <span class="badge bg-warning">早餐</span>
                                        {% elif order.meal_period == 'lunch' %}
                                            <span class="badge bg-success">午餐</span>
                                        {% elif order.meal_period == 'dinner' %}
                                            <span class="badge bg-primary">晚餐</span>
                                        {% else %}
                                            <span class="badge bg-secondary">晚餐</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ order.waiter.full_name if order.waiter else '未分配' }}</td>
                                    <td>{{ order.created_at.strftime('%Y-%m-%d %H:%M') if order.created_at else '无' }}</td>
                                    <td>
                                        {% if order.dining_end_time %}
                                            {{ order.dining_end_time.strftime('%Y-%m-%d %H:%M') }}
                                        {% elif order.completed_at %}
                                            {{ order.completed_at.strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td onclick="event.stopPropagation()">
                                        <div class="btn-group-vertical btn-group-sm">
                                            {% if order.status == 'reserved' and not order.waiter %}
                                            <button type="button" class="btn btn-success btn-sm mb-1" title="授权服务员"
                                                    onclick="event.stopPropagation(); authorizeWaiter('{{ order.table.number }}', {{ order.id }})">
                                                <i class="bi bi-person-check"></i> 授权
                                            </button>
                                            {% endif %}

                                            {% if user.role.value == 'manager' and order.waiter %}
                                                {% if order.status.value == 'completed' %}
                                                    <button type="button" class="btn btn-secondary btn-sm mb-1" title="已完成的订单不允许更换服务员" disabled>
                                                        <i class="bi bi-person-x"></i> 换人
                                                    </button>
                                                {% else %}
                                                    <button type="button" class="btn btn-warning btn-sm mb-1" title="更换服务员"
                                                            onclick="event.stopPropagation(); changeWaiter('{{ order.table.number }}', {{ order.id }})">
                                                        <i class="bi bi-person-x"></i> 换人
                                                    </button>
                                                {% endif %}
                                            {% endif %}

                                            <button type="button" class="btn btn-primary btn-sm mb-1" title="编辑订单"
                                                    onclick="event.stopPropagation(); editOrder({{ order.id }})">
                                                <i class="bi bi-pencil"></i> 编辑
                                            </button>

                                            {% if order.status in ['pending_start', 'serving'] and user.role.value == 'manager' %}
                                            <button type="button" class="btn btn-danger btn-sm" title="餐饮经理强制结束包厢"
                                                    onclick="event.stopPropagation(); forceEndRoom('{{ order.table.number }}')">
                                                <i class="bi bi-stop-circle-fill"></i> 强制结束
                                            </button>
                                            {% endif %}

                                            {% if order.status in ['pending_start', 'serving', 'reserved', 'confirmed'] and user.role.value == 'admin' %}
                                            <button type="button" class="btn btn-danger btn-sm" title="系统管理员强制结束用餐"
                                                    onclick="event.stopPropagation(); adminForceEndDining('{{ order.table.number }}')">
                                                <i class="bi bi-power"></i> 强制结束
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-inbox display-1 text-muted"></i>
                        <p class="text-muted mt-2">暂无订单数据</p>
                        {% if user.is_service_staff %}
                        <a href="/orders/create" class="btn btn-primary">
                            <i class="bi bi-plus"></i> 创建第一个订单
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 授权服务员模态框 -->
<div class="modal fade" id="authorizeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="authorizeModalTitle">授权服务员</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">选择服务员</label>
                    <select class="form-select" id="waiterId">
                        <option value="">正在加载服务员列表...</option>
                    </select>
                </div>
                <div class="alert alert-info">
                    <small>只能授权给服务员角色的用户。授权后该包厢状态将变为"已预订"，服务员可以操作此包厢。</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmAuthorizeBtn">确认授权</button>
            </div>
        </div>
    </div>
</div>

<!-- 更换服务员模态框 -->
<div class="modal fade" id="changeWaiterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changeWaiterModalTitle">更换服务员</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">当前服务员</label>
                    <input type="text" class="form-control" id="currentWaiterName" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">选择新服务员</label>
                    <select class="form-select" id="newWaiterId">
                        <option value="">正在加载可用服务员...</option>
                    </select>
                </div>
                <div class="alert alert-warning">
                    <small>更换服务员后，原服务员将被设置为空闲状态，新服务员将接管此包厢。</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" id="confirmChangeWaiterBtn">确认更换</button>
            </div>
        </div>
    </div>
</div>

<!-- 系统管理员强制结束用餐模态框 -->
<div class="modal fade" id="adminForceEndModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="adminForceEndModalTitle">系统管理员强制结束用餐</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle-fill"></i>
                    <strong>警告：</strong>此操作将强制结束包厢用餐，跳过所有常规检查，无法撤销！
                </div>
                <div class="mb-3">
                    <label class="form-label">包厢号</label>
                    <input type="text" class="form-control" id="adminForceEndRoomNumber" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">强制结束原因 *</label>
                    <select class="form-select" id="adminForceEndReason" required>
                        <option value="">请选择原因</option>
                        <option value="客户要求">客户要求</option>
                        <option value="设备故障">设备故障</option>
                        <option value="紧急情况">紧急情况</option>
                        <option value="系统维护">系统维护</option>
                        <option value="其他原因">其他原因</option>
                    </select>
                </div>
                <div class="mb-3" id="customReasonDiv" style="display: none;">
                    <label class="form-label">详细说明</label>
                    <textarea class="form-control" id="customReasonText" rows="3" placeholder="请详细说明强制结束的原因..."></textarea>
                </div>
                <div class="alert alert-info">
                    <small>
                        <strong>此操作将：</strong><br>
                        • 立即结束包厢用餐<br>
                        • 更新订单状态为已完成<br>
                        • 释放包厢为可用状态<br>
                        • 记录操作日志
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmAdminForceEndBtn">
                    <i class="bi bi-power"></i> 确认强制结束
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 订单详情模态框 -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="orderDetailsModalTitle">订单详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderDetailsModalBody">
                <!-- 订单详情内容将在这里动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="editOrderBtn">编辑订单</button>
                <button type="button" class="btn btn-info" id="printOrderBtn">打印订单</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    var currentOrderId = null;
    var currentRoomNumber = null;

    function showSuccess(message) {
        alert('成功: ' + message);
    }

    function showError(message) {
        alert('错误: ' + message);
    }



    // 订单详情查看
    function showOrderDetails(orderId) {
        console.log('显示订单详情:', orderId);
        currentOrderId = orderId;
        
        fetch('/api/orders/' + orderId + '/details', {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include'
        })
        .then(function(response) {
            if (!response.ok) throw new Error('HTTP ' + response.status);
            return response.json();
        })
        .then(function(data) {
            if (data.success) {
                showOrderDetailsInModal(data.order);
            } else {
                showError('获取订单详情失败: ' + (data.message || '未知错误'));
            }
        })
        .catch(function(error) {
            showError('获取订单详情失败: ' + error.message);
        });
    }

    function showOrderDetailsInModal(order) {
        document.getElementById('orderDetailsModalTitle').textContent = '订单详情 - ' + (order.table ? order.table.number + '包厢' : '无包厢');
        
        var modalBody = document.getElementById('orderDetailsModalBody');
        modalBody.innerHTML = '';
        
        // 基本信息
        var basicInfo = document.createElement('div');
        basicInfo.className = 'mb-4';
        basicInfo.innerHTML = '<h6>基本信息</h6>';
        
        var infoList = document.createElement('ul');
        infoList.className = 'list-unstyled';
        
        var infoItems = [
            '客户姓名: ' + (order.customer_name || '无'),
            '联系电话: ' + (order.customer_phone || '无'),
            '用餐人数: ' + (order.guest_count || 0) + '人',
            '用餐标准: ¥' + (order.dining_standard_amount || order.total_amount || 0) + '/桌',
            '订单状态: ' + getOrderStatusText(order.status),
            '用餐时段: ' + getMealPeriodText(order.meal_period),
            '服务员: ' + (order.waiter ? order.waiter.full_name : '未分配'),
            '创建时间: ' + formatDateTime(order.created_at),
            '开始时间: ' + (order.started_at ? formatDateTime(order.started_at) : '未开始')
        ];
        
        for (var i = 0; i < infoItems.length; i++) {
            var li = document.createElement('li');
            li.textContent = infoItems[i];
            li.style.marginBottom = '5px';
            infoList.appendChild(li);
        }
        
        basicInfo.appendChild(infoList);
        modalBody.appendChild(basicInfo);
        
        // 菜品列表
        if (order.items && order.items.length > 0) {
            var itemsSection = document.createElement('div');
            itemsSection.innerHTML = '<h6>菜品列表 (' + order.items.length + '项)</h6>';
            
            for (var j = 0; j < order.items.length; j++) {
                var item = order.items[j];
                var itemDiv = document.createElement('div');
                itemDiv.className = 'border rounded p-2 mb-2';
                itemDiv.textContent = (j + 1) + '. ' + item.dish_name + ' (状态: ' + getItemStatusText(item.status) + ')';
                itemsSection.appendChild(itemDiv);
            }
            
            modalBody.appendChild(itemsSection);
        }
        
        // 特殊要求
        if (order.special_requests) {
            var specialDiv = document.createElement('div');
            specialDiv.className = 'mt-3';
            specialDiv.innerHTML = '<h6>特殊要求</h6>';
            var reqP = document.createElement('p');
            reqP.textContent = order.special_requests;
            specialDiv.appendChild(reqP);
            modalBody.appendChild(specialDiv);
        }
        
        // 设置按钮事件
        document.getElementById('editOrderBtn').onclick = function() { editOrder(order.id); };
        document.getElementById('printOrderBtn').onclick = function() { printOrder(order.id); };
        
        // 显示模态框
        var modal = new bootstrap.Modal(document.getElementById('orderDetailsModal'));
        modal.show();
    }

    // 编辑订单
    function editOrder(orderId) {
        window.location.href = '/orders/' + orderId + '/edit';
    }

    // 打印订单 - 最简版本，避免任何可能的代码泄露
    function printOrder(orderId) {
        alert('打印功能被调用，订单ID: ' + orderId + '。实际项目中这里会调用打印API。');
    }

    // 授权服务员
    function authorizeWaiter(roomNumber, orderId) {
        currentRoomNumber = roomNumber;
        currentOrderId = orderId;
        
        document.getElementById('authorizeModalTitle').textContent = '授权服务员 - ' + roomNumber + '包厢';
        document.getElementById('waiterId').innerHTML = '<option value="">正在加载服务员列表...</option>';
        
        var modal = new bootstrap.Modal(document.getElementById('authorizeModal'));
        modal.show();
        
        loadWaitersForAuthorize();
    }

    function loadWaitersForAuthorize() {
        fetch('/api/users?role=waiter', {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include'
        })
        .then(function(response) {
            if (!response.ok) throw new Error('HTTP ' + response.status);
            return response.json();
        })
        .then(function(data) {
            var waiterList = Array.isArray(data) ? data : (data.waiters || []);
            var waiterSelect = document.getElementById('waiterId');
            
            if (waiterList.length === 0) {
                waiterSelect.innerHTML = '<option value="">没有可用的服务员</option>';
            } else {
                var optionsHtml = '<option value="">请选择服务员</option>';
                for (var i = 0; i < waiterList.length; i++) {
                    var waiter = waiterList[i];
                    optionsHtml += '<option value="' + waiter.id + '">' + waiter.full_name + ' (' + waiter.username + ')</option>';
                }
                waiterSelect.innerHTML = optionsHtml;
            }
        })
        .catch(function(error) {
            document.getElementById('waiterId').innerHTML = '<option value="">加载失败，请重试</option>';
            showError('获取服务员列表失败: ' + error.message);
        });
    }

    document.getElementById('confirmAuthorizeBtn').onclick = function() {
        var waiterId = document.getElementById('waiterId').value;
        if (!waiterId) {
            showError('请选择服务员');
            return;
        }

        fetch('/tables/' + currentRoomNumber + '/assign-waiter', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ waiter_id: parseInt(waiterId) })
        })
        .then(function(response) {
            if (!response.ok) throw new Error('HTTP ' + response.status);
            return response.json();
        })
        .then(function(data) {
            if (data.success) {
                showSuccess(data.message || '授权成功');
                var modal = bootstrap.Modal.getInstance(document.getElementById('authorizeModal'));
                if (modal) modal.hide();
                setTimeout(function() { location.reload(); }, 1500);
            } else {
                showError(data.message || '授权失败');
            }
        })
        .catch(function(error) {
            showError('授权失败: ' + error.message);
        });
    };

    // 更换服务员
    function changeWaiter(roomNumber, orderId) {
        currentRoomNumber = roomNumber;
        currentOrderId = orderId;

        document.getElementById('changeWaiterModalTitle').textContent = '更换服务员 - ' + roomNumber + '包厢';
        document.getElementById('newWaiterId').innerHTML = '<option value="">正在加载可用服务员...</option>';

        // 获取当前订单信息
        fetch('/api/orders/' + orderId + '/details', {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include'
        })
        .then(function(response) {
            if (!response.ok) throw new Error('HTTP ' + response.status);
            return response.json();
        })
        .then(function(data) {
            if (data.success && data.order) {
                // 检查订单状态
                if (data.order.status === 'completed') {
                    alert('已完成的订单不允许更换服务员');
                    return;
                }

                if (data.order.waiter) {
                    document.getElementById('currentWaiterName').value = data.order.waiter.full_name + ' (' + data.order.waiter.username + ')';
                } else {
                    document.getElementById('currentWaiterName').value = '无';
                }
            } else {
                document.getElementById('currentWaiterName').value = '无';
            }
        })
        .catch(function(error) {
            document.getElementById('currentWaiterName').value = '获取失败';
        });

        var modal = new bootstrap.Modal(document.getElementById('changeWaiterModal'));
        modal.show();

        loadAvailableWaiters();
    }

    function loadAvailableWaiters() {
        fetch('/api/users?role=waiter&status=idle', {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include'
        })
        .then(function(response) {
            if (!response.ok) throw new Error('HTTP ' + response.status);
            return response.json();
        })
        .then(function(data) {
            var waiterList = Array.isArray(data) ? data : (data.waiters || []);
            var waiterSelect = document.getElementById('newWaiterId');

            if (waiterList.length === 0) {
                waiterSelect.innerHTML = '<option value="">没有可用的服务员</option>';
            } else {
                var optionsHtml = '<option value="">请选择新服务员</option>';
                for (var i = 0; i < waiterList.length; i++) {
                    var waiter = waiterList[i];
                    optionsHtml += '<option value="' + waiter.id + '">' + waiter.full_name + ' (' + waiter.username + ')</option>';
                }
                waiterSelect.innerHTML = optionsHtml;
            }
        })
        .catch(function(error) {
            document.getElementById('newWaiterId').innerHTML = '<option value="">加载失败，请重试</option>';
            showError('获取可用服务员列表失败: ' + error.message);
        });
    }

    document.getElementById('confirmChangeWaiterBtn').onclick = function() {
        var newWaiterId = document.getElementById('newWaiterId').value;
        if (!newWaiterId) {
            showError('请选择新服务员');
            return;
        }

        fetch('/api/orders/' + currentOrderId + '/change-waiter', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ new_waiter_id: parseInt(newWaiterId) })
        })
        .then(function(response) {
            if (!response.ok) throw new Error('HTTP ' + response.status);
            return response.json();
        })
        .then(function(data) {
            if (data.success) {
                showSuccess(data.message || '服务员更换成功');
                var modal = bootstrap.Modal.getInstance(document.getElementById('changeWaiterModal'));
                if (modal) modal.hide();
                setTimeout(function() { location.reload(); }, 1500);
            } else {
                showError(data.message || '更换服务员失败');
            }
        })
        .catch(function(error) {
            showError('更换服务员失败: ' + error.message);
        });
    };

    // 强制结束包厢
    function forceEndRoom(roomNumber) {
        var confirmMsg = '确定要强制结束' + roomNumber + '包厢吗？\n\n此操作将：\n- 结束当前用餐\n- 清除服务员授权\n- 包厢状态变为空闲\n- 取消所有未完成订单\n- 无法撤销\n\n注意：此功能仅限餐饮经理使用';

        if (confirm(confirmMsg)) {
            fetch('/tables/' + roomNumber + '/force-end', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include'
            })
            .then(function(response) {
                if (response.status === 403) {
                    return response.json().then(function(data) {
                        throw new Error('权限不足：' + (data.detail || '只有餐饮经理可以强制结束包厢'));
                    });
                }
                if (!response.ok) throw new Error('HTTP ' + response.status + ' - 请求失败');
                return response.json();
            })
            .then(function(data) {
                if (data.success) {
                    showSuccess(data.message || '包厢已强制结束');
                    setTimeout(function() { location.reload(); }, 1500);
                } else {
                    showError(data.message || '强制结束包厢失败');
                }
            })
            .catch(function(error) {
                console.error('强制结束包厢失败:', error);
                showError('强制结束包厢失败: ' + error.message);
            });
        }
    }

    // 系统管理员强制结束用餐
    function adminForceEndDining(roomNumber) {
        // 设置包厢号
        document.getElementById('adminForceEndRoomNumber').value = roomNumber;
        document.getElementById('adminForceEndModalTitle').textContent = `强制结束 ${roomNumber} 包厢用餐`;

        // 重置表单
        document.getElementById('adminForceEndReason').value = '';
        document.getElementById('customReasonText').value = '';
        document.getElementById('customReasonDiv').style.display = 'none';

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('adminForceEndModal'));
        modal.show();
    }

    // 辅助函数
    function formatDateTime(dateStr) {
        if (!dateStr) return '-';
        return new Date(dateStr).toLocaleString('zh-CN');
    }

    function getOrderStatusText(status) {
        var statusMap = {
            'reserved': '已预订',
            'pending_start': '待开始',
            'serving': '已上菜',
            'completed': '已完成',
            'cancelled': '已取消'
        };
        return statusMap[status] || status;
    }

    function getItemStatusText(status) {
        var statusMap = {
            'pending': '待制作',
            'cooking': '制作中',
            'ready': '已完成',
            'served': '已上菜',
            'cancelled': '已取消'
        };
        return statusMap[status] || '待制作';
    }

    function getMealPeriodText(period) {
        var periodMap = {
            'breakfast': '早餐',
            'lunch': '午餐',
            'dinner': '晚餐'
        };
        return periodMap[period] || '晚餐';
    }

    // 确保模态框关闭按钮正常工作
    document.addEventListener('DOMContentLoaded', function() {
        // 系统管理员强制结束用餐相关事件
        const reasonSelect = document.getElementById('adminForceEndReason');
        const customReasonDiv = document.getElementById('customReasonDiv');

        if (reasonSelect) {
            reasonSelect.addEventListener('change', function() {
                if (this.value === '其他原因') {
                    customReasonDiv.style.display = 'block';
                } else {
                    customReasonDiv.style.display = 'none';
                }
            });
        }

        // 确认强制结束按钮事件
        const confirmBtn = document.getElementById('confirmAdminForceEndBtn');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', function() {
                const roomNumber = document.getElementById('adminForceEndRoomNumber').value;
                const reason = document.getElementById('adminForceEndReason').value;
                const customReason = document.getElementById('customReasonText').value;

                if (!reason) {
                    alert('请选择强制结束原因');
                    return;
                }

                if (reason === '其他原因' && !customReason.trim()) {
                    alert('请填写详细说明');
                    return;
                }

                const finalReason = reason === '其他原因' ? customReason : reason;

                // 显示加载状态
                confirmBtn.disabled = true;
                confirmBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 处理中...';

                fetch('/admin/force-end-dining', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        room_number: roomNumber,
                        reason: finalReason
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSuccess(data.message || '强制结束用餐成功');

                        // 关闭模态框
                        const modal = bootstrap.Modal.getInstance(document.getElementById('adminForceEndModal'));
                        if (modal) {
                            modal.hide();
                        }

                        setTimeout(() => location.reload(), 1500);
                    } else {
                        showError(data.message || '强制结束失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showError('强制结束失败，请重试');
                })
                .finally(() => {
                    // 恢复按钮状态
                    confirmBtn.disabled = false;
                    confirmBtn.innerHTML = '<i class="bi bi-power"></i> 确认强制结束';
                });
            });
        }

        // 为所有模态框的关闭按钮添加事件监听器
        var closeButtons = document.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                var modal = button.closest('.modal');
                if (modal) {
                    var modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    } else {
                        // 如果没有实例，创建一个新的并隐藏
                        modalInstance = new bootstrap.Modal(modal);
                        modalInstance.hide();
                    }
                }
            });
        });

        // 为模态框背景点击添加关闭功能
        var modals = document.querySelectorAll('.modal');
        modals.forEach(function(modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    var modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                }
            });
        });
    });
</script>
{% endblock %}
