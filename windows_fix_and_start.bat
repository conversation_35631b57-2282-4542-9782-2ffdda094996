@echo off
setlocal enabledelayedexpansion

REM Set UTF-8 encoding to handle Chinese characters properly
chcp 65001 >nul 2>&1

title Jiyang Lake Hotel System - Windows Fix and Start

echo ==========================================
echo Jiyang Lake Hotel Management System
echo Windows Environment Fix and Start
echo ==========================================
echo.

REM Step 1: Check Python installation
echo [1/6] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in system PATH
    echo.
    echo Please install Python 3.8+ from: https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo SUCCESS: Python !PYTHON_VERSION! detected
)
echo.

REM Step 2: Check current directory
echo [2/6] Checking project directory...
if not exist "backend_pure_python" (
    echo ERROR: backend_pure_python directory not found
    echo Current directory: %CD%
    echo.
    echo Please ensure you are running this script from the project root directory
    echo The directory should contain: backend_pure_python folder
    echo.
    pause
    exit /b 1
) else (
    echo SUCCESS: Project directory structure verified
)
echo.

REM Step 3: Navigate to backend directory
echo [3/6] Entering backend directory...
cd backend_pure_python
if errorlevel 1 (
    echo ERROR: Failed to enter backend_pure_python directory
    pause
    exit /b 1
) else (
    echo SUCCESS: Now in backend_pure_python directory
)
echo.

REM Step 4: Setup virtual environment
echo [4/6] Setting up Python virtual environment...
if exist "venv" (
    echo Virtual environment already exists, checking integrity...
    if exist "venv\Scripts\python.exe" (
        echo SUCCESS: Virtual environment is valid
    ) else (
        echo WARNING: Virtual environment is corrupted, recreating...
        rmdir /s /q venv 2>nul
        python -m venv venv
        if errorlevel 1 (
            echo ERROR: Failed to recreate virtual environment
            pause
            exit /b 1
        )
    )
) else (
    echo Creating new virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ERROR: Failed to create virtual environment
        echo.
        echo Possible solutions:
        echo 1. Run as Administrator
        echo 2. Check if antivirus is blocking Python
        echo 3. Ensure sufficient disk space
        echo.
        pause
        exit /b 1
    ) else (
        echo SUCCESS: Virtual environment created
    )
)
echo.

REM Step 5: Activate virtual environment and install dependencies
echo [5/6] Activating virtual environment and installing dependencies...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment
    pause
    exit /b 1
)

echo Upgrading pip...
python -m pip install --upgrade pip >nul 2>&1

echo Installing required packages...
pip install fastapi uvicorn sqlalchemy python-jose passlib python-multipart pydantic pydantic-settings jinja2 aiofiles bcrypt cryptography -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    echo.
    echo Trying alternative installation method...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Both installation methods failed
        echo.
        echo Please check:
        echo 1. Internet connection
        echo 2. Firewall/proxy settings
        echo 3. Python pip configuration
        echo.
        pause
        exit /b 1
    )
)
echo SUCCESS: Dependencies installed
echo.

REM Step 6: Initialize database if needed
echo [6/6] Checking and initializing database...
if not exist "paocai.db" (
    echo Database not found, initializing...
    python init_db.py
    if errorlevel 1 (
        echo ERROR: Failed to initialize database
        pause
        exit /b 1
    ) else (
        echo SUCCESS: Database initialized
    )
) else (
    echo SUCCESS: Database already exists
)
echo.

REM Final step: Start the application
echo ==========================================
echo Starting Jiyang Lake Hotel Management System
echo ==========================================
echo.
echo System Information:
echo - Server URL: http://localhost:5109
echo - API Documentation: http://localhost:5109/docs
echo - System Status: Starting...
echo.
echo Press Ctrl+C to stop the service
echo ==========================================
echo.

REM Start the main application
python main.py

REM If we reach here, the server has stopped
echo.
echo ==========================================
echo Server has stopped
echo ==========================================
echo.
echo If the server stopped unexpectedly, please check:
echo 1. Port 5109 is not occupied by another application
echo 2. No firewall blocking the application
echo 3. All dependencies are properly installed
echo.
echo Press any key to exit...
pause >nul
