import socketio
from typing import Dict, List
import json
import asyncio

# 创建 Socket.IO 服务器
sio = socketio.AsyncServer(
    cors_allowed_origins="*",
    logger=True,
    engineio_logger=True
)

# 存储连接的客户端信息
connected_clients: Dict[str, Dict] = {}


@sio.event
async def connect(sid, environ):
    """客户端连接事件"""
    print(f"客户端 {sid} 已连接")
    connected_clients[sid] = {
        "connected_at": asyncio.get_event_loop().time(),
        "user_id": None,
        "role": None,
        "room": None
    }


@sio.event
async def disconnect(sid):
    """客户端断开连接事件"""
    print(f"客户端 {sid} 已断开连接")
    if sid in connected_clients:
        del connected_clients[sid]


@sio.event
async def join_room(sid, data):
    """加入房间"""
    try:
        room = data.get("room")
        user_id = data.get("user_id")
        role = data.get("role")
        
        if room and user_id and role:
            await sio.enter_room(sid, room)
            connected_clients[sid].update({
                "user_id": user_id,
                "role": role,
                "room": room
            })
            
            await sio.emit("room_joined", {
                "room": room,
                "message": f"已加入 {room} 房间"
            }, room=sid)
            
            print(f"用户 {user_id} ({role}) 加入房间 {room}")
        else:
            await sio.emit("error", {"message": "缺少必要参数"}, room=sid)
            
    except Exception as e:
        print(f"加入房间错误: {e}")
        await sio.emit("error", {"message": "加入房间失败"}, room=sid)


@sio.event
async def leave_room(sid, data):
    """离开房间"""
    try:
        room = data.get("room")
        if room:
            await sio.leave_room(sid, room)
            if sid in connected_clients:
                connected_clients[sid]["room"] = None
            
            await sio.emit("room_left", {
                "room": room,
                "message": f"已离开 {room} 房间"
            }, room=sid)
            
    except Exception as e:
        print(f"离开房间错误: {e}")


# 业务相关的 Socket 事件
@sio.event
async def order_update(sid, data):
    """订单更新事件"""
    try:
        order_id = data.get("order_id")
        status = data.get("status")
        table_id = data.get("table_id")
        
        # 广播给相关房间
        rooms_to_notify = ["management", "kitchen", f"table_{table_id}"]
        
        for room in rooms_to_notify:
            await sio.emit("order_status_changed", {
                "order_id": order_id,
                "status": status,
                "table_id": table_id,
                "timestamp": asyncio.get_event_loop().time()
            }, room=room)
            
    except Exception as e:
        print(f"订单更新错误: {e}")


@sio.event
async def dish_status_update(sid, data):
    """菜品状态更新事件"""
    try:
        dish_id = data.get("dish_id")
        order_id = data.get("order_id")
        status = data.get("status")
        table_id = data.get("table_id")
        
        # 广播给相关房间
        rooms_to_notify = ["management", "kitchen", f"table_{table_id}", "waiters"]
        
        for room in rooms_to_notify:
            await sio.emit("dish_status_changed", {
                "dish_id": dish_id,
                "order_id": order_id,
                "status": status,
                "table_id": table_id,
                "timestamp": asyncio.get_event_loop().time()
            }, room=room)
            
    except Exception as e:
        print(f"菜品状态更新错误: {e}")


@sio.event
async def voice_notification(sid, data):
    """语音通知事件"""
    try:
        message = data.get("message")
        audio_url = data.get("audio_url")
        target_room = data.get("target_room", "kitchen")
        
        await sio.emit("voice_alert", {
            "message": message,
            "audio_url": audio_url,
            "timestamp": asyncio.get_event_loop().time()
        }, room=target_room)
        
    except Exception as e:
        print(f"语音通知错误: {e}")


# 工具函数
async def broadcast_to_role(role: str, event: str, data: dict):
    """向特定角色的所有客户端广播消息"""
    for sid, client_info in connected_clients.items():
        if client_info.get("role") == role:
            await sio.emit(event, data, room=sid)


async def broadcast_to_room(room: str, event: str, data: dict):
    """向特定房间广播消息"""
    await sio.emit(event, data, room=room)


async def get_connected_users_by_role(role: str) -> List[Dict]:
    """获取特定角色的在线用户"""
    users = []
    for sid, client_info in connected_clients.items():
        if client_info.get("role") == role:
            users.append({
                "sid": sid,
                "user_id": client_info.get("user_id"),
                "room": client_info.get("room"),
                "connected_at": client_info.get("connected_at")
            })
    return users
