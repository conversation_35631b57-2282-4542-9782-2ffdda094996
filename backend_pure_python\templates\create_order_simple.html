{% extends "base.html" %}

{% block title %}新建订单 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-plus-circle"></i>
        新建订单
    </h1>
</div>

<form method="post" action="/orders/create">
    <div class="row">
        <!-- 订单信息 -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">订单信息</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="table_id" class="form-label">选择包厢 *</label>
                        <select class="form-select" id="table_id" name="table_id" required>
                            <option value="">请选择包厢</option>
                            {% for table in tables %}
                            <option value="{{ table.id }}" 
                                    {% if request.query_params.get('table_id') == table.id|string %}selected{% endif %}>
                                {{ table.number }} - {{ table.name or '' }} 
                                ({{ table.capacity }}人)
                                {% if table.table_type.value == 'vip_room' %} [VIP]{% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="customer_name" class="form-label">客户姓名</label>
                        <input type="text" class="form-control" id="customer_name" name="customer_name" 
                               placeholder="客户姓名（可选）">
                    </div>
                    
                    <div class="mb-3">
                        <label for="guest_count" class="form-label">客人数量 *</label>
                        <input type="number" class="form-control" id="guest_count" name="guest_count"
                               value="1" min="1" max="50" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">用餐时段 *</label>
                        <div class="row">
                            <div class="col-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="meal_period" id="breakfast" value="breakfast">
                                    <label class="form-check-label" for="breakfast">
                                        <i class="bi bi-sunrise"></i> 早餐
                                    </label>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="meal_period" id="lunch" value="lunch">
                                    <label class="form-check-label" for="lunch">
                                        <i class="bi bi-sun"></i> 午餐
                                    </label>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="meal_period" id="dinner" value="dinner" checked>
                                    <label class="form-check-label" for="dinner">
                                        <i class="bi bi-moon"></i> 晚餐
                                    </label>
                                </div>
                            </div>
                        </div>
                        <small class="form-text text-muted">默认选择晚餐时段</small>
                    </div>

                    <div class="mb-3">
                        <label for="dining_standard" class="form-label">用餐标准 *</label>
                        <div class="input-group">
                            <span class="input-group-text">¥</span>
                            <input type="number" class="form-control" id="dining_standard" name="dining_standard"
                                   step="0.01" min="0" placeholder="0.00" required>
                            <span class="input-group-text">元/桌</span>
                        </div>
                        <div class="form-text">请输入整桌用餐标准金额</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="menu_content" class="form-label">菜单内容</label>
                        <textarea class="form-control" id="menu_content" name="menu_content"
                                  rows="8" placeholder="请输入完整菜单，每行一个菜品，例如：
A餐前手工酸奶
手工面筋竹林鸡
野茭白烧河虾
手捏菜炒肚尖
樟树港小炒肉
清蒸大白丝
焖蛋烩三鲜
咸菜炒春笋
蚌肉金花菜
有机香米饭"></textarea>
                        <div class="form-text">
                            <span id="dish-count">0</span> 个菜品
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="special_requests" class="form-label">特殊要求</label>
                        <textarea class="form-control" id="special_requests" name="special_requests"
                                  rows="3" placeholder="特殊要求或备注"></textarea>
                    </div>
                </div>
            </div>
            
            <!-- 订单汇总 -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">订单汇总</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <span>小计:</span>
                        <span id="subtotal">¥0.00</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>服务费:</span>
                        <span id="service-charge">¥0.00</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between fw-bold">
                        <span>总计:</span>
                        <span id="total" class="text-primary">¥0.00</span>
                    </div>
                    
                    <div class="d-grid gap-2 mt-3">
                        <button type="submit" class="btn btn-primary" id="submit-btn">
                            <i class="bi bi-check-circle"></i>
                            确认下单
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearOrder()">
                            <i class="bi bi-arrow-clockwise"></i>
                            清空订单
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 菜单预览 -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">菜单预览</h5>
                    <small class="text-muted">系统将根据输入的菜单自动解析每个菜品</small>
                </div>
                <div class="card-body">
                    <div id="menu-preview" class="bg-light p-3 rounded" style="min-height: 200px;">
                        <div class="text-center text-muted py-5">
                            <i class="bi bi-journal-text" style="font-size: 3rem;"></i>
                            <p class="mt-2">请在左侧输入菜单内容</p>
                        </div>
                    </div>

                    <div class="mt-3">
                        <h6>菜单示例：</h6>
                        <div class="bg-info bg-opacity-10 p-3 rounded">
                            <div class="small">
                                A餐前手工酸奶<br>
                                手工面筋竹林鸡<br>
                                野茭白烧河虾<br>
                                手捏菜炒肚尖<br>
                                樟树港小炒肉<br>
                                清蒸大白丝<br>
                                焖蛋烩三鲜<br>
                                咸菜炒春笋<br>
                                蚌肉金花菜<br>
                                有机香米饭
                            </div>
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="useExample()">
                            <i class="bi bi-clipboard"></i>
                            使用示例菜单
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

{% endblock %}

{% block extra_js %}
<script>
    // 实时更新菜单预览和统计
    document.getElementById('menu_content').addEventListener('input', function() {
        const content = this.value.trim();
        const dishes = content ? content.split('\n').filter(line => line.trim()) : [];

        // 检查重复菜品
        const dishNames = [];
        const duplicateDishes = [];
        const duplicateIndexes = [];

        dishes.forEach((dish, index) => {
            if (dishNames.includes(dish)) {
                if (!duplicateDishes.includes(dish)) {
                    duplicateDishes.push(dish);
                }
                duplicateIndexes.push(index);
            } else {
                dishNames.push(dish);
            }
        });

        // 检查菜品数量限制
        const maxDishes = 50; // 设置最大菜品数量
        const isOverLimit = dishes.length > maxDishes;

        // 更新菜品数量显示
        const dishCountElement = document.getElementById('dish-count');
        let countText = dishes.length + ' 个菜品';

        if (isOverLimit) {
            countText += ` <span class="text-danger">(超出限制，最多${maxDishes}个)</span>`;
        }

        if (duplicateDishes.length > 0) {
            countText += ` <span class="text-warning">(发现${duplicateDishes.length}个重复菜品)</span>`;
        }

        dishCountElement.innerHTML = countText;

        // 更新菜单预览
        const preview = document.getElementById('menu-preview');
        if (dishes.length > 0) {
            let html = '<h6>菜品列表：</h6><ol class="mb-0">';
            dishes.forEach((dish, index) => {
                let itemClass = '';
                if (duplicateIndexes.includes(index)) {
                    itemClass = 'text-warning fw-bold';
                }
                if (index >= maxDishes) {
                    itemClass = 'text-danger text-decoration-line-through';
                }
                html += `<li class="mb-1 ${itemClass}">${dish.trim()}`;
                if (duplicateIndexes.includes(index)) {
                    html += ' <i class="bi bi-exclamation-triangle text-warning" title="重复菜品"></i>';
                }
                if (index >= maxDishes) {
                    html += ' <i class="bi bi-x-circle text-danger" title="超出限制"></i>';
                }
                html += '</li>';
            });
            html += '</ol>';

            // 添加警告信息
            if (duplicateDishes.length > 0 || isOverLimit) {
                html += '<div class="mt-3 alert alert-warning alert-sm">';
                if (duplicateDishes.length > 0) {
                    html += `<i class="bi bi-exclamation-triangle"></i> 发现重复菜品：${duplicateDishes.join('、')}<br>`;
                }
                if (isOverLimit) {
                    html += `<i class="bi bi-x-circle"></i> 菜品数量超出限制，最多允许${maxDishes}个菜品`;
                }
                html += '</div>';
            }

            preview.innerHTML = html;
        } else {
            preview.innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="bi bi-journal-text" style="font-size: 3rem;"></i>
                    <p class="mt-2">请在左侧输入菜单内容</p>
                </div>
            `;
        }

        // 更新总计（暂时显示菜品数量）
        const validDishCount = Math.min(dishes.length, maxDishes);
        document.getElementById('subtotal').textContent = validDishCount + ' 个菜品';
        document.getElementById('total').textContent = validDishCount + ' 个菜品';

        // 存储验证状态供提交时使用
        window.menuValidation = {
            hasDuplicates: duplicateDishes.length > 0,
            isOverLimit: isOverLimit,
            duplicateDishes: duplicateDishes,
            maxDishes: maxDishes,
            actualCount: dishes.length
        };
    });

    // 使用示例菜单
    function useExample() {
        const example = `A餐前手工酸奶
手工面筋竹林鸡
野茭白烧河虾
手捏菜炒肚尖
樟树港小炒肉
清蒸大白丝
焖蛋烩三鲜
咸菜炒春笋
蚌肉金花菜
有机香米饭`;

        document.getElementById('menu_content').value = example;
        document.getElementById('customer_name').value = '张先生';
        document.getElementById('guest_count').value = '8';

        // 触发更新
        document.getElementById('menu_content').dispatchEvent(new Event('input'));
    }

    function clearOrder() {
        if (confirm('确定要清空当前订单吗？')) {
            document.querySelector('form').reset();
            document.getElementById('menu_content').dispatchEvent(new Event('input'));
        }
    }

    // 防抖变量
    let isSubmitting = false;
    let submitTimeout = null;

    // 表单Ajax提交
    document.querySelector('form').addEventListener('submit', function(e) {
        e.preventDefault();

        // 防抖检查
        if (isSubmitting) {
            showError('订单正在创建中，请勿重复提交');
            return false;
        }

        const submitBtn = document.getElementById('submit-btn');
        const originalText = submitBtn.innerHTML;

        // 验证表单
        const tableId = document.getElementById('table_id').value;
        const menuContent = document.getElementById('menu_content').value.trim();

        if (!tableId) {
            showError('请选择包厢');
            return false;
        }

        if (!menuContent) {
            showError('请输入菜单内容');
            return false;
        }

        // 检查菜单验证状态
        if (window.menuValidation) {
            const validation = window.menuValidation;

            // 检查重复菜品
            if (validation.hasDuplicates) {
                const duplicateList = validation.duplicateDishes.join('、');
                const confirmMessage = `⚠️ 发现重复菜品\n\n重复的菜品：${duplicateList}\n\n系统将自动去除重复项，是否继续创建订单？`;

                if (!confirm(confirmMessage)) {
                    showError('请修改菜单后重新提交');
                    return false;
                }
            }

            // 检查菜品数量限制
            if (validation.isOverLimit) {
                const confirmMessage = `⚠️ 菜品数量超出限制\n\n当前菜品数量：${validation.actualCount}个\n最大允许数量：${validation.maxDishes}个\n\n系统将只保留前${validation.maxDishes}个菜品，是否继续创建订单？`;

                if (!confirm(confirmMessage)) {
                    showError('请减少菜品数量后重新提交');
                    return false;
                }
            }
        }

        // 设置提交状态
        isSubmitting = true;

        // 显示加载状态
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 创建中...';

        // 设置超时保护（30秒后自动恢复）
        submitTimeout = setTimeout(() => {
            isSubmitting = false;
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
            showError('提交超时，请重试');
        }, 30000);

        // 准备表单数据
        const formData = new FormData(this);

        // 提交订单
        fetch('/orders/create', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // 清除超时保护
            if (submitTimeout) {
                clearTimeout(submitTimeout);
                submitTimeout = null;
            }

            if (data.success) {
                showSuccess(data.message || '订单创建成功');

                // 清空表单
                this.reset();
                document.getElementById('menu_content').dispatchEvent(new Event('input'));

                // 可选择性跳转到订单列表
                setTimeout(() => {
                    if (confirm('订单创建成功！是否跳转到订单管理页面？')) {
                        window.location.href = '/orders';
                    }
                }, 2000);
            } else {
                showError(data.message || '创建订单失败');
            }
        })
        .catch(error => {
            // 清除超时保护
            if (submitTimeout) {
                clearTimeout(submitTimeout);
                submitTimeout = null;
            }

            console.error('Error:', error);
            showError('创建订单失败，请重试');
        })
        .finally(() => {
            // 恢复提交状态
            isSubmitting = false;

            // 恢复按钮状态
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });

        return false;
    });

    // 初始化
    document.getElementById('menu_content').dispatchEvent(new Event('input'));
</script>
{% endblock %}
