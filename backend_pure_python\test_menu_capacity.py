#!/usr/bin/env python3
"""
测试菜单容量限制和重复菜品检查
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8001"

def test_menu_capacity():
    """测试菜单容量限制"""
    print("🧪 测试菜单容量限制和重复菜品检查")
    print("=" * 60)
    
    # 测试1: 正常菜单（10个菜品）
    print("1. 测试正常菜单（10个菜品）:")
    normal_menu = """A餐前手工酸奶
手工面筋竹林鸡
野茭白烧河虾
手捏菜炒肚尖
樟树港小炒肉
清蒸大白丝
焖蛋烩三鲜
咸菜炒春笋
蚌肉金花菜
有机香米饭"""
    
    result = create_order_test("正常菜单测试", normal_menu, 1)
    print(f"结果: {'✅ 成功' if result else '❌ 失败'}")
    print()
    
    # 测试2: 大菜单（28个菜品）
    print("2. 测试大菜单（28个菜品）:")
    large_menu = """A餐前手工酸奶
手工面筋竹林鸡
野茭白烧河虾
手捏菜炒肚尖
樟树港小炒肉
清蒸大白丝
焖蛋烩三鲜
咸菜炒春笋
蚌肉金花菜
有机香米饭
红烧肉
糖醋排骨
宫保鸡丁
麻婆豆腐
鱼香肉丝
回锅肉
水煮鱼
口水鸡
夫妻肺片
蒜泥白肉
白切鸡
清蒸鲈鱼
红烧鲤鱼
糖醋鱼
酸菜鱼
剁椒鱼头
蒸蛋羹
炒青菜"""
    
    result = create_order_test("大菜单测试", large_menu, 2)
    print(f"结果: {'✅ 成功' if result else '❌ 失败'}")
    print()
    
    # 测试3: 超大菜单（50个菜品）
    print("3. 测试超大菜单（50个菜品）:")
    huge_menu_items = [
        "A餐前手工酸奶", "手工面筋竹林鸡", "野茭白烧河虾", "手捏菜炒肚尖", "樟树港小炒肉",
        "清蒸大白丝", "焖蛋烩三鲜", "咸菜炒春笋", "蚌肉金花菜", "有机香米饭",
        "红烧肉", "糖醋排骨", "宫保鸡丁", "麻婆豆腐", "鱼香肉丝",
        "回锅肉", "水煮鱼", "口水鸡", "夫妻肺片", "蒜泥白肉",
        "白切鸡", "清蒸鲈鱼", "红烧鲤鱼", "糖醋鱼", "酸菜鱼",
        "剁椒鱼头", "蒸蛋羹", "炒青菜", "凉拌黄瓜", "拍黄瓜",
        "凉拌木耳", "凉拌海带", "凉拌豆腐皮", "凉拌粉丝", "凉拌萝卜丝",
        "热干面", "牛肉面", "鸡蛋面", "西红柿鸡蛋面", "炸酱面",
        "小笼包", "煎饺", "蒸饺", "锅贴", "包子",
        "馒头", "花卷", "油条", "豆浆", "稀饭"
    ]
    huge_menu = "\n".join(huge_menu_items)
    
    result = create_order_test("超大菜单测试", huge_menu, 3)
    print(f"结果: {'✅ 成功' if result else '❌ 失败'}")
    print()
    
    # 测试4: 重复菜品检查
    print("4. 测试重复菜品检查:")
    duplicate_menu = """A餐前手工酸奶
手工面筋竹林鸡
野茭白烧河虾
手工面筋竹林鸡
樟树港小炒肉
清蒸大白丝
野茭白烧河虾
咸菜炒春笋
蚌肉金花菜
有机香米饭"""
    
    result = create_order_test("重复菜品测试", duplicate_menu, 4)
    print(f"结果: {'❌ 应该失败' if not result else '⚠️ 意外成功'}")
    print()
    
    # 测试5: 空菜单
    print("5. 测试空菜单:")
    empty_menu = ""
    
    result = create_order_test("空菜单测试", empty_menu, 5)
    print(f"结果: {'❌ 应该失败' if not result else '⚠️ 意外成功'}")
    print()
    
    print("🎯 测试完成!")

def create_order_test(test_name, menu_content, table_id):
    """创建订单测试"""
    try:
        # 准备表单数据
        form_data = {
            'table_id': str(table_id),
            'customer_name': f'测试客户-{test_name}',
            'guest_count': '8',
            'dining_standard': '888.0',
            'menu_content': menu_content,
            'special_requests': f'{test_name}的特殊要求',
            'meal_period': 'dinner'
        }
        
        # 发送请求
        response = requests.post(
            f"{BASE_URL}/orders/create",
            data=form_data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('success'):
                    print(f"   ✅ 成功: {result.get('message', '订单创建成功')}")
                    if 'order_number' in result:
                        print(f"   📋 订单号: {result['order_number']}")
                    return True
                else:
                    print(f"   ❌ 失败: {result.get('message', '未知错误')}")
                    return False
            except json.JSONDecodeError:
                print(f"   ⚠️ 响应不是JSON格式")
                print(f"   响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误信息: {error_data.get('message', '未知错误')}")
            except:
                print(f"   响应内容: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 网络错误: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
        return False

def check_database_limits():
    """检查数据库字段限制"""
    print("📊 检查数据库字段限制:")
    print("   - special_requests: Text 类型（无长度限制）")
    print("   - dish_name: String(100) 类型（最大100字符）")
    print("   - customer_name: String(100) 类型（最大100字符）")
    print("   - order_number: String(50) 类型（最大50字符）")
    print()

if __name__ == "__main__":
    check_database_limits()
    test_menu_capacity()
