#!/usr/bin/env python3
"""
添加"用餐开始"指令模板
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from core.database import get_db
from models.command_template import CommandTemplate

def add_dining_start_command():
    """添加用餐开始指令模板"""
    db = next(get_db())
    
    try:
        print("🔄 添加用餐开始指令模板...")
        
        # 检查是否已存在
        existing = db.query(CommandTemplate).filter(CommandTemplate.code == 'dining_start').first()
        if existing:
            print("✅ 用餐开始指令模板已存在")
            return
        
        # 创建新指令模板
        command = CommandTemplate(
            name='用餐开始',
            code='dining_start',
            description='服务员通知厨房用餐开始，需要输入实际用餐人数',
            voice_text='请上菜，用餐人数',
            category='service',
            is_active=True,
            sort_order=1  # 设置为第一个
        )
        
        db.add(command)
        db.commit()
        print("✅ 用餐开始指令模板添加成功")
        
        # 配置输入设定
        config_file = "command_input_configs.json"
        configs = {}
        
        # 读取现有配置
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    configs = json.load(f)
            except Exception as e:
                print(f"⚠️ 读取配置文件失败: {e}")
                configs = {}
        
        # 添加用餐开始指令的输入配置
        configs['dining_start'] = {
            'allow_input': True,
            'input_placeholder': '请输入实际用餐人数',
            'input_required': True
        }
        
        # 保存配置
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(configs, f, ensure_ascii=False, indent=2)
            print("✅ 用餐开始指令输入配置添加成功")
        except Exception as e:
            print(f"❌ 保存输入配置失败: {e}")
        
    except Exception as e:
        db.rollback()
        print(f"❌ 添加用餐开始指令模板失败: {e}")
        raise
    finally:
        db.close()

def show_current_commands():
    """显示当前所有指令模板"""
    db = next(get_db())
    
    try:
        print("\n📋 当前指令模板列表:")
        commands = db.query(CommandTemplate).order_by(CommandTemplate.sort_order, CommandTemplate.name).all()
        
        for cmd in commands:
            status = "✅" if cmd.is_active else "❌"
            print(f"  {status} {cmd.name} ({cmd.code}) - {cmd.category}")
        
        # 显示输入配置
        config_file = "command_input_configs.json"
        if os.path.exists(config_file):
            print("\n⚙️ 输入配置:")
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    configs = json.load(f)
                for code, config in configs.items():
                    if config.get('allow_input'):
                        required = "必填" if config.get('input_required') else "可选"
                        placeholder = config.get('input_placeholder', '')
                        print(f"  📝 {code}: {required} - {placeholder}")
            except Exception as e:
                print(f"⚠️ 读取输入配置失败: {e}")
        
    except Exception as e:
        print(f"❌ 获取指令模板失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("🏨 暨阳湖大酒店传菜管理系统 - 添加用餐开始指令")
    print("=" * 50)
    
    # 显示当前状态
    print("添加前状态:")
    show_current_commands()
    
    # 添加指令
    add_dining_start_command()
    
    # 显示添加后状态
    print("\n添加后状态:")
    show_current_commands()
