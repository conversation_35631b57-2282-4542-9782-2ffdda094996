from sqlalchemy import Column, Integer, String, Boolean, Float, Text, DateTime
from sqlalchemy.sql import func
from core.database import Base


class SystemConfig(Base):
    """系统配置模型"""
    __tablename__ = "system_configs"
    
    id = Column(Integer, primary_key=True, index=True)
    config_key = Column(String(100), unique=True, index=True, nullable=False, comment="配置键")
    config_value = Column(Text, nullable=False, comment="配置值")
    config_type = Column(String(20), nullable=False, default="string", comment="配置类型: string, int, float, bool, json")
    category = Column(String(50), nullable=False, default="general", comment="配置分类")
    description = Column(String(500), nullable=True, comment="配置描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<SystemConfig(key='{self.config_key}', value='{self.config_value}')>"
    
    @property
    def typed_value(self):
        """根据配置类型返回正确类型的值"""
        if self.config_type == "int":
            return int(self.config_value)
        elif self.config_type == "float":
            return float(self.config_value)
        elif self.config_type == "bool":
            return self.config_value.lower() in ('true', '1', 'yes', 'on')
        elif self.config_type == "json":
            import json
            return json.loads(self.config_value)
        else:
            return self.config_value


# 默认系统配置
DEFAULT_SYSTEM_CONFIGS = [
    {
        "config_key": "waiter_auto_refresh_interval",
        "config_value": "15",
        "config_type": "int",
        "category": "waiter",
        "description": "服务员界面自动刷新间隔时间（秒）"
    },
    {
        "config_key": "waiter_auto_refresh_enabled",
        "config_value": "true",
        "config_type": "bool",
        "category": "waiter",
        "description": "是否启用服务员界面自动刷新"
    },
    {
        "config_key": "kitchen_display_auto_flip_interval",
        "config_value": "8",
        "config_type": "int",
        "category": "kitchen",
        "description": "厨房大屏自动翻页间隔时间（秒）"
    },
    {
        "config_key": "system_timezone",
        "config_value": "Asia/Shanghai",
        "config_type": "string",
        "category": "general",
        "description": "系统时区设置"
    },
    {
        "config_key": "order_auto_complete_timeout",
        "config_value": "120",
        "config_type": "int",
        "category": "order",
        "description": "订单自动完成超时时间（分钟）"
    },
    # 厨房大屏设置
    {
        "config_key": "kitchen_display_rooms_per_page",
        "config_value": "5",
        "config_type": "int",
        "category": "kitchen_display",
        "description": "厨房大屏每页显示包厢数量（3-8个）"
    },
    {
        "config_key": "kitchen_display_font_size",
        "config_value": "14",
        "config_type": "int",
        "category": "kitchen_display",
        "description": "厨房大屏菜品字体大小（10-16px）"
    },
    {
        "config_key": "kitchen_display_layout_mode",
        "config_value": "auto",
        "config_type": "string",
        "category": "kitchen_display",
        "description": "厨房大屏布局模式（auto/manual）"
    },
    {
        "config_key": "kitchen_display_auto_flip_enabled",
        "config_value": "true",
        "config_type": "bool",
        "category": "kitchen_display",
        "description": "是否启用厨房大屏自动翻页"
    },
    {
        "config_key": "kitchen_display_hide_navigation",
        "config_value": "true",
        "config_type": "bool",
        "category": "kitchen_display",
        "description": "厨房大屏是否隐藏导航菜单"
    },
    {
        "config_key": "kitchen_display_grid_columns",
        "config_value": "3",
        "config_type": "int",
        "category": "kitchen_display",
        "description": "厨房大屏手动布局列数"
    },
    {
        "config_key": "kitchen_display_grid_rows",
        "config_value": "2",
        "config_type": "int",
        "category": "kitchen_display",
        "description": "厨房大屏手动布局行数"
    },
    {
        "config_key": "kitchen_display_room_width",
        "config_value": "370",
        "config_type": "int",
        "category": "kitchen_display",
        "description": "厨房大屏包厢显示宽度（px，建议300-400）"
    },
    {
        "config_key": "kitchen_display_auto_font_size",
        "config_value": "true",
        "config_type": "bool",
        "category": "kitchen_display",
        "description": "厨房大屏菜品字体自适应大小"
    },
    # 移动端界面设置
    {
        "config_key": "mobile_font_size_normal",
        "config_value": "16",
        "config_type": "int",
        "category": "mobile",
        "description": "移动端正文字体大小（14-18px）"
    },
    {
        "config_key": "mobile_font_size_title",
        "config_value": "20",
        "config_type": "int",
        "category": "mobile",
        "description": "移动端标题字体大小（18-24px）"
    },
    # 打荷操作页面设置
    {
        "config_key": "kitchen_helper_font_size",
        "config_value": "16",
        "config_type": "int",
        "category": "kitchen_helper",
        "description": "打荷页面字体大小（12-24px）"
    },
    {
        "config_key": "kitchen_helper_font_color",
        "config_value": "#ffffff",
        "config_type": "string",
        "category": "kitchen_helper",
        "description": "打荷页面字体颜色"
    },
    {
        "config_key": "kitchen_helper_theme",
        "config_value": "dark",
        "config_type": "string",
        "category": "kitchen_helper",
        "description": "打荷页面主题（dark/light）"
    },
    {
        "config_key": "kitchen_helper_border_size",
        "config_value": "2",
        "config_type": "int",
        "category": "kitchen_helper",
        "description": "打荷页面边框大小（1-5px）"
    },
    {
        "config_key": "kitchen_helper_container_width",
        "config_value": "auto",
        "config_type": "string",
        "category": "kitchen_helper",
        "description": "打荷页面容器宽度（auto/fixed）"
    },
    {
        "config_key": "kitchen_helper_padding_top",
        "config_value": "8",
        "config_type": "int",
        "category": "kitchen_helper",
        "description": "打荷页面菜品标签上边距（px）"
    },
    {
        "config_key": "kitchen_helper_padding_bottom",
        "config_value": "8",
        "config_type": "int",
        "category": "kitchen_helper",
        "description": "打荷页面菜品标签下边距（px）"
    },
    {
        "config_key": "kitchen_helper_padding_left",
        "config_value": "12",
        "config_type": "int",
        "category": "kitchen_helper",
        "description": "打荷页面菜品标签左边距（px）"
    },
    {
        "config_key": "kitchen_helper_padding_right",
        "config_value": "12",
        "config_type": "int",
        "category": "kitchen_helper",
        "description": "打荷页面菜品标签右边距（px）"
    },
    {
        "config_key": "kitchen_helper_rooms_per_row",
        "config_value": "5",
        "config_type": "int",
        "category": "kitchen_helper",
        "description": "打荷页面每行显示包厢数量（3-8个）"
    },
    {
        "config_key": "kitchen_helper_room_gap",
        "config_value": "10",
        "config_type": "int",
        "category": "kitchen_helper",
        "description": "打荷页面包厢间距（px）"
    },
    {
        "config_key": "mobile_font_size_instruction",
        "config_value": "18",
        "config_type": "int",
        "category": "mobile",
        "description": "移动端指令字体大小（16-20px）"
    },
    {
        "config_key": "mobile_button_min_size",
        "config_value": "44",
        "config_type": "int",
        "category": "mobile",
        "description": "移动端按钮最小尺寸（44px×44px）"
    }

]
