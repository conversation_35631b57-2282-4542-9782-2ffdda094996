"""
语音播报记录模型
用于跟踪每条指令的播报次数和状态
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from core.database import Base
from datetime import datetime


class VoiceBroadcastLog(Base):
    """语音播报记录表"""
    __tablename__ = "voice_broadcast_logs"

    id = Column(Integer, primary_key=True, index=True)
    
    # 关联的指令ID
    waiter_action_id = Column(Integer, ForeignKey("waiter_actions.id"), nullable=False)
    
    # 播报内容
    broadcast_message = Column(String(500), nullable=False)
    
    # 播报次数
    broadcast_count = Column(Integer, default=0)
    
    # 最大播报次数（从系统配置获取）
    max_broadcast_count = Column(Integer, default=2)
    
    # 播报状态
    is_completed = Column(Boolean, default=False)  # 是否播报完成
    
    # 时间戳
    first_broadcast_at = Column(DateTime, nullable=True)  # 首次播报时间
    last_broadcast_at = Column(DateTime, nullable=True)   # 最后播报时间
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    waiter_action = relationship("WaiterAction", back_populates="voice_broadcast_logs")

    def __repr__(self):
        return f"<VoiceBroadcastLog(id={self.id}, action_id={self.waiter_action_id}, count={self.broadcast_count}/{self.max_broadcast_count})>"

    def can_broadcast(self):
        """检查是否还可以播报"""
        return not self.is_completed and self.broadcast_count < self.max_broadcast_count

    def record_broadcast(self):
        """记录一次播报"""
        self.broadcast_count += 1
        self.last_broadcast_at = datetime.utcnow()
        
        if self.first_broadcast_at is None:
            self.first_broadcast_at = datetime.utcnow()
        
        # 检查是否播报完成
        if self.broadcast_count >= self.max_broadcast_count:
            self.is_completed = True
        
        self.updated_at = datetime.utcnow()

    def reset_broadcast(self):
        """重置播报状态（用于重新播报）"""
        self.broadcast_count = 0
        self.is_completed = False
        self.first_broadcast_at = None
        self.last_broadcast_at = None
        self.updated_at = datetime.utcnow()
