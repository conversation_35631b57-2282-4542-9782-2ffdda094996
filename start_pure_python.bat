@echo off
chcp 65001 >nul 2>&1
title Jiyang Lake Hotel Food Service Management System (Pure Python)

echo ==========================================
echo Jiyang Lake Hotel Food Service Management System
echo Pure Python Version
echo ==========================================

REM Check Python version
echo Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not installed, please install Python 3.9+
    pause
    exit /b 1
)

echo.
echo Select operation:
echo 1. Full start (Initialize database + Start service)
echo 2. Start service only
echo 3. Initialize database only
echo 4. Install dependencies
echo 5. Reset database
set /p choice=Please select (1-5):

if "%choice%"=="1" goto full_start
if "%choice%"=="2" goto start_only
if "%choice%"=="3" goto init_db_only
if "%choice%"=="4" goto install_deps
if "%choice%"=="5" goto reset_db
echo Invalid selection
pause
exit /b 1

:full_start
echo Starting system completely...

REM Enter pure Python version directory
cd backend_pure_python

REM Create virtual environment
echo Creating Python virtual environment...
python -m venv venv 2>nul
call venv\Scripts\activate.bat

REM Upgrade pip
echo Upgrading pip...
pip install --upgrade pip

REM Install dependencies
echo Installing Python dependencies...
pip install fastapi uvicorn sqlalchemy python-jose passlib python-multipart pydantic pydantic-settings jinja2 aiofiles bcrypt cryptography

REM Initialize database
echo Initializing database...
python init_db.py

REM Start service
echo Starting service...
echo.
echo SUCCESS: System started successfully!
echo Web Access: http://localhost:5109
echo API Documentation: http://localhost:5109/docs
echo.
echo Press Ctrl+C to stop service
echo.

python main.py
goto :eof

:start_only
echo Starting service...
cd backend_pure_python
python -m venv venv 2>nul
call venv\Scripts\activate.bat
pip install -r requirements.txt
python main.py
goto :eof

:init_db_only
echo Initializing database...
cd backend_pure_python
python -m venv venv 2>nul
call venv\Scripts\activate.bat
pip install -r requirements.txt
python init_db.py
echo SUCCESS: Database initialization completed
pause
goto :eof

:install_deps
echo Installing dependencies...
cd backend_pure_python
python -m venv venv 2>nul
call venv\Scripts\activate.bat
pip install -r requirements.txt
echo SUCCESS: Dependencies installation completed
pause
goto :eof

:reset_db
echo Resetting database...
cd backend_pure_python

REM Delete database file
if exist "paocai.db" (
    del paocai.db
    echo Deleted old database file
)

python -m venv venv 2>nul
call venv\Scripts\activate.bat
pip install -r requirements.txt
python init_db.py
echo SUCCESS: Database reset completed
pause
goto :eof
