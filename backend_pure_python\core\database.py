from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from .config import settings

# 创建数据库引擎 - 性能优化配置
engine = create_engine(
    settings.DATABASE_URL,
    # SQLite 特定配置
    connect_args={
        "check_same_thread": False,
        "timeout": 20,  # 数据库锁超时
        "isolation_level": None  # 自动提交模式
    } if "sqlite" in settings.DATABASE_URL else {
        "charset": "utf8mb4",
        "use_unicode": True
    },
    echo=False,  # 关闭SQL日志提升性能
    pool_size=5,  # 连接池大小
    max_overflow=10,  # 最大溢出连接
    pool_timeout=30,  # 连接超时
    pool_recycle=3600,  # 连接回收时间
    pool_pre_ping=True  # 连接预检查
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()


def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
