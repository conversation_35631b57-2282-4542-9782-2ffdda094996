import { 
  UserRole, 
  TableType, 
  TableStatus, 
  DishCategory, 
  DishStatus, 
  SpicyLevel, 
  CookingMethod,
  OrderStatus,
  OrderType,
  PaymentMethod,
  DishItemStatus
} from '@/types';

// 用户角色标签映射
export const USER_ROLE_LABELS: Record<UserRole, string> = {
  [UserRole.ADMIN]: '系统管理员',
  [UserRole.MANAGER]: '餐饮经理',
  [UserRole.DEPUTY_MANAGER]: '餐饮部副经理',
  [UserRole.WAITER]: '服务员',
  [UserRole.CHEF_MANAGER]: '厨师长',
  [UserRole.CHEF]: '厨师',
  [UserRole.KITCHEN_HELPER]: '打荷员',
  [UserRole.CASHIER]: '收银员',
};

// 餐桌类型标签映射
export const TABLE_TYPE_LABELS: Record<TableType, string> = {
  [TableType.PRIVATE_ROOM]: '包厢',
  [TableType.HALL_TABLE]: '大厅餐桌',
  [TableType.VIP_ROOM]: 'VIP包厢',
  [TableType.OUTDOOR]: '户外餐桌',
};

// 餐桌状态标签映射
export const TABLE_STATUS_LABELS: Record<TableStatus, string> = {
  [TableStatus.AVAILABLE]: '空闲可用',
  [TableStatus.RESERVED]: '已预订',
  [TableStatus.OCCUPIED]: '使用中',
  [TableStatus.CLEANING]: '清理中',
  [TableStatus.MAINTENANCE]: '维护中',
  [TableStatus.OUT_OF_SERVICE]: '停用',
};

// 餐桌状态颜色映射
export const TABLE_STATUS_COLORS: Record<TableStatus, string> = {
  [TableStatus.AVAILABLE]: 'success',
  [TableStatus.RESERVED]: 'warning',
  [TableStatus.OCCUPIED]: 'error',
  [TableStatus.CLEANING]: 'processing',
  [TableStatus.MAINTENANCE]: 'default',
  [TableStatus.OUT_OF_SERVICE]: 'default',
};

// 菜品分类标签映射
export const DISH_CATEGORY_LABELS: Record<DishCategory, string> = {
  [DishCategory.COLD_DISH]: '凉菜',
  [DishCategory.HOT_DISH]: '热菜',
  [DishCategory.SOUP]: '汤羹',
  [DishCategory.STAPLE]: '主食',
  [DishCategory.DESSERT]: '甜品',
  [DishCategory.BEVERAGE]: '饮品',
  [DishCategory.ALCOHOL]: '酒类',
  [DishCategory.SEAFOOD]: '海鲜',
  [DishCategory.VEGETARIAN]: '素食',
  [DishCategory.SPECIALTY]: '招牌菜',
};

// 菜品状态标签映射
export const DISH_STATUS_LABELS: Record<DishStatus, string> = {
  [DishStatus.AVAILABLE]: '可供应',
  [DishStatus.UNAVAILABLE]: '暂不供应',
  [DishStatus.SEASONAL]: '时令菜',
  [DishStatus.LIMITED]: '限量供应',
  [DishStatus.DISCONTINUED]: '已停售',
};

// 菜品状态颜色映射
export const DISH_STATUS_COLORS: Record<DishStatus, string> = {
  [DishStatus.AVAILABLE]: 'success',
  [DishStatus.UNAVAILABLE]: 'default',
  [DishStatus.SEASONAL]: 'warning',
  [DishStatus.LIMITED]: 'processing',
  [DishStatus.DISCONTINUED]: 'error',
};

// 辣度标签映射
export const SPICY_LEVEL_LABELS: Record<SpicyLevel, string> = {
  [SpicyLevel.NONE]: '不辣',
  [SpicyLevel.MILD]: '微辣',
  [SpicyLevel.MEDIUM]: '中辣',
  [SpicyLevel.HOT]: '辣',
  [SpicyLevel.EXTRA_HOT]: '特辣',
};

// 烹饪方式标签映射
export const COOKING_METHOD_LABELS: Record<CookingMethod, string> = {
  [CookingMethod.STIR_FRY]: '炒',
  [CookingMethod.STEAM]: '蒸',
  [CookingMethod.BOIL]: '煮',
  [CookingMethod.BRAISE]: '红烧',
  [CookingMethod.GRILL]: '烤',
  [CookingMethod.DEEP_FRY]: '炸',
  [CookingMethod.COLD_MIX]: '凉拌',
  [CookingMethod.STEW]: '炖',
  [CookingMethod.ROAST]: '烘烤',
};

// 订单状态标签映射
export const ORDER_STATUS_LABELS: Record<OrderStatus, string> = {
  [OrderStatus.DRAFT]: '草稿',
  [OrderStatus.PENDING_KITCHEN]: '待厨房确认',
  [OrderStatus.CONFIRMED]: '已确认',
  [OrderStatus.PENDING_START]: '待起菜',
  [OrderStatus.IN_PROGRESS]: '服务中',
  [OrderStatus.PENDING_PAYMENT]: '待结算',
  [OrderStatus.PAID]: '已结账',
  [OrderStatus.COMPLETED]: '已完成',
  [OrderStatus.CANCELLED]: '已取消',
};

// 订单状态颜色映射
export const ORDER_STATUS_COLORS: Record<OrderStatus, string> = {
  [OrderStatus.DRAFT]: 'default',
  [OrderStatus.PENDING_KITCHEN]: 'warning',
  [OrderStatus.CONFIRMED]: 'processing',
  [OrderStatus.PENDING_START]: 'warning',
  [OrderStatus.IN_PROGRESS]: 'processing',
  [OrderStatus.PENDING_PAYMENT]: 'warning',
  [OrderStatus.PAID]: 'success',
  [OrderStatus.COMPLETED]: 'success',
  [OrderStatus.CANCELLED]: 'error',
};

// 订单类型标签映射
export const ORDER_TYPE_LABELS: Record<OrderType, string> = {
  [OrderType.DINE_IN]: '堂食',
  [OrderType.TAKEAWAY]: '外带',
  [OrderType.DELIVERY]: '外送',
  [OrderType.RESERVATION]: '预订',
};

// 支付方式标签映射
export const PAYMENT_METHOD_LABELS: Record<PaymentMethod, string> = {
  [PaymentMethod.CASH]: '现金',
  [PaymentMethod.CARD]: '刷卡',
  [PaymentMethod.WECHAT]: '微信支付',
  [PaymentMethod.ALIPAY]: '支付宝',
  [PaymentMethod.MEMBER_CARD]: '会员卡',
  [PaymentMethod.VOUCHER]: '代金券',
};

// 菜品项状态标签映射
export const DISH_ITEM_STATUS_LABELS: Record<DishItemStatus, string> = {
  [DishItemStatus.PENDING_CONFIRM]: '待确认',
  [DishItemStatus.CONFIRMED]: '已确认',
  [DishItemStatus.PENDING_COOK]: '待制作',
  [DishItemStatus.COOKING]: '制作中',
  [DishItemStatus.READY]: '待上菜',
  [DishItemStatus.SERVED]: '已上菜',
  [DishItemStatus.CANCELLED]: '已取消',
};

// 菜品项状态颜色映射
export const DISH_ITEM_STATUS_COLORS: Record<DishItemStatus, string> = {
  [DishItemStatus.PENDING_CONFIRM]: 'default',
  [DishItemStatus.CONFIRMED]: 'processing',
  [DishItemStatus.PENDING_COOK]: 'warning',
  [DishItemStatus.COOKING]: 'processing',
  [DishItemStatus.READY]: 'success',
  [DishItemStatus.SERVED]: 'success',
  [DishItemStatus.CANCELLED]: 'error',
};

// 默认分页配置
export const DEFAULT_PAGE_SIZE = 20;
export const PAGE_SIZE_OPTIONS = ['10', '20', '50', '100'];

// 文件上传配置
export const UPLOAD_CONFIG = {
  maxSize: 10 * 1024 * 1024, // 10MB
  acceptedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  acceptedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
};

// 时间格式
export const DATE_FORMAT = 'YYYY-MM-DD';
export const TIME_FORMAT = 'HH:mm:ss';
export const DATETIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';

// 货币格式
export const CURRENCY_SYMBOL = '¥';

// 应用主题色
export const THEME_COLORS = {
  primary: '#1890ff',
  success: '#52c41a',
  warning: '#faad14',
  error: '#f5222d',
  jiyang: '#f2750a',
};
