from sqlalchemy import Column, Integer, String, Boolean, DateTime, Enum, Text, Numeric, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from core.database import Base


class OrderStatus(str, enum.Enum):
    """订单状态枚举"""
    DRAFT = "draft"                    # 草稿
    PENDING_KITCHEN = "pending_kitchen"  # 待厨房确认
    CONFIRMED = "confirmed"            # 已确认
    PENDING_START = "pending_start"    # 待起菜
    IN_PROGRESS = "in_progress"        # 服务中
    PENDING_PAYMENT = "pending_payment"  # 待结算
    PAID = "paid"                      # 已结账
    COMPLETED = "completed"            # 已完成
    CANCELLED = "cancelled"            # 已取消


class OrderType(str, enum.Enum):
    """订单类型枚举"""
    DINE_IN = "dine_in"        # 堂食
    TAKEAWAY = "takeaway"      # 外带
    DELIVERY = "delivery"      # 外送
    RESERVATION = "reservation"  # 预订


class PaymentMethod(str, enum.Enum):
    """支付方式枚举"""
    CASH = "cash"              # 现金
    CARD = "card"              # 刷卡
    WECHAT = "wechat"          # 微信支付
    ALIPAY = "alipay"          # 支付宝
    MEMBER_CARD = "member_card"  # 会员卡
    VOUCHER = "voucher"        # 代金券


class DishItemStatus(str, enum.Enum):
    """菜品项状态枚举"""
    PENDING_CONFIRM = "pending_confirm"  # 待确认
    CONFIRMED = "confirmed"              # 已确认
    PENDING_COOK = "pending_cook"        # 待制作
    COOKING = "cooking"                  # 制作中
    READY = "ready"                      # 待上菜
    SERVED = "served"                    # 已上菜
    CANCELLED = "cancelled"              # 已取消


class Order(Base):
    """订单模型"""
    __tablename__ = "orders"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 订单基本信息
    order_number = Column(String(50), unique=True, index=True, nullable=False, comment="订单号")
    order_type = Column(Enum(OrderType), nullable=False, default=OrderType.DINE_IN, comment="订单类型")
    status = Column(Enum(OrderStatus), nullable=False, default=OrderStatus.DRAFT, comment="订单状态")
    
    # 关联信息
    table_id = Column(Integer, ForeignKey("tables.id"), nullable=True, comment="餐桌ID")
    customer_id = Column(Integer, nullable=True, comment="客户ID")
    waiter_id = Column(Integer, ForeignKey("users.id"), nullable=True, comment="服务员ID")
    
    # 客户信息
    customer_name = Column(String(100), nullable=True, comment="客户姓名")
    customer_phone = Column(String(20), nullable=True, comment="客户电话")
    guest_count = Column(Integer, nullable=False, default=1, comment="用餐人数")
    
    # 预订信息
    reservation_time = Column(DateTime(timezone=True), nullable=True, comment="预订时间")
    estimated_duration = Column(Integer, nullable=True, comment="预计用餐时长(分钟)")
    
    # 金额信息
    subtotal = Column(Numeric(10, 2), nullable=False, default=0, comment="小计")
    service_charge = Column(Numeric(10, 2), nullable=False, default=0, comment="服务费")
    discount_amount = Column(Numeric(10, 2), nullable=False, default=0, comment="折扣金额")
    total_amount = Column(Numeric(10, 2), nullable=False, default=0, comment="总金额")
    paid_amount = Column(Numeric(10, 2), nullable=False, default=0, comment="已付金额")
    
    # 支付信息
    payment_method = Column(Enum(PaymentMethod), nullable=True, comment="支付方式")
    payment_time = Column(DateTime(timezone=True), nullable=True, comment="支付时间")
    payment_reference = Column(String(100), nullable=True, comment="支付凭证号")
    
    # 折扣信息
    discount_type = Column(String(50), nullable=True, comment="折扣类型")
    discount_rate = Column(Numeric(5, 2), nullable=True, comment="折扣率")
    coupon_code = Column(String(50), nullable=True, comment="优惠券代码")
    
    # 特殊要求
    special_requests = Column(Text, nullable=True, comment="特殊要求")
    notes = Column(Text, nullable=True, comment="备注")
    
    # 时间信息
    ordered_at = Column(DateTime(timezone=True), nullable=True, comment="下单时间")
    confirmed_at = Column(DateTime(timezone=True), nullable=True, comment="确认时间")
    started_at = Column(DateTime(timezone=True), nullable=True, comment="开始制作时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    table = relationship("Table", back_populates="orders")
    waiter = relationship("User", foreign_keys=[waiter_id])
    order_items = relationship("OrderItem", back_populates="order", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Order(id={self.id}, number='{self.order_number}', status='{self.status}')>"
    
    @property
    def is_editable(self) -> bool:
        """是否可编辑"""
        return self.status in [OrderStatus.DRAFT, OrderStatus.PENDING_KITCHEN, OrderStatus.CONFIRMED]
    
    @property
    def can_cancel(self) -> bool:
        """是否可取消"""
        return self.status not in [OrderStatus.PAID, OrderStatus.COMPLETED, OrderStatus.CANCELLED]
    
    def calculate_total(self):
        """计算订单总金额"""
        self.subtotal = sum(item.total_price for item in self.order_items if item.status != DishItemStatus.CANCELLED)
        self.total_amount = self.subtotal + self.service_charge - self.discount_amount


class OrderItem(Base):
    """订单项模型"""
    __tablename__ = "order_items"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 关联信息
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False, comment="订单ID")
    dish_id = Column(Integer, ForeignKey("dishes.id"), nullable=False, comment="菜品ID")
    
    # 菜品信息快照
    dish_name = Column(String(100), nullable=False, comment="菜品名称")
    dish_price = Column(Numeric(10, 2), nullable=False, comment="菜品单价")
    dish_category = Column(String(50), nullable=True, comment="菜品分类")
    
    # 订购信息
    quantity = Column(Integer, nullable=False, default=1, comment="数量")
    unit_price = Column(Numeric(10, 2), nullable=False, comment="单价")
    total_price = Column(Numeric(10, 2), nullable=False, comment="小计")
    
    # 状态信息
    status = Column(Enum(DishItemStatus), nullable=False, default=DishItemStatus.PENDING_CONFIRM, comment="状态")
    
    # 制作信息
    assigned_chef_id = Column(Integer, nullable=True, comment="分配的厨师ID")
    kitchen_notes = Column(Text, nullable=True, comment="厨房备注")
    special_requirements = Column(Text, nullable=True, comment="特殊要求")
    
    # 时间信息
    ordered_at = Column(DateTime(timezone=True), nullable=True, comment="下单时间")
    confirmed_at = Column(DateTime(timezone=True), nullable=True, comment="确认时间")
    started_cooking_at = Column(DateTime(timezone=True), nullable=True, comment="开始制作时间")
    ready_at = Column(DateTime(timezone=True), nullable=True, comment="制作完成时间")
    served_at = Column(DateTime(timezone=True), nullable=True, comment="上菜时间")
    
    # 催菜信息
    rush_count = Column(Integer, default=0, comment="催菜次数")
    last_rush_at = Column(DateTime(timezone=True), nullable=True, comment="最后催菜时间")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    order = relationship("Order", back_populates="order_items")
    dish = relationship("Dish", back_populates="order_items")
    assigned_chef = relationship("User", foreign_keys=[assigned_chef_id])
    
    def __repr__(self):
        return f"<OrderItem(id={self.id}, dish='{self.dish_name}', quantity={self.quantity})>"
    
    @property
    def can_rush(self) -> bool:
        """是否可以催菜"""
        if self.status not in [DishItemStatus.PENDING_COOK, DishItemStatus.COOKING]:
            return False
        
        # 检查催菜冷却时间（5分钟）
        if self.last_rush_at:
            from datetime import datetime, timedelta
            cooldown = timedelta(minutes=5)
            return datetime.utcnow() - self.last_rush_at > cooldown
        
        return True
    
    @property
    def cooking_duration(self) -> int:
        """制作时长（分钟）"""
        if self.started_cooking_at and self.ready_at:
            duration = self.ready_at - self.started_cooking_at
            return int(duration.total_seconds() / 60)
        return 0
    
    def update_status(self, new_status: DishItemStatus):
        """更新状态并记录时间"""
        from datetime import datetime
        
        self.status = new_status
        now = datetime.utcnow()
        
        if new_status == DishItemStatus.CONFIRMED:
            self.confirmed_at = now
        elif new_status == DishItemStatus.COOKING:
            self.started_cooking_at = now
        elif new_status == DishItemStatus.READY:
            self.ready_at = now
        elif new_status == DishItemStatus.SERVED:
            self.served_at = now
