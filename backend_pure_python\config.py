"""
服务器配置模块
用于配置服务器端口和其他设置
"""

class ServerConfig:
    """服务器配置类"""

    # 默认端口配置
    DEFAULT_PORT = 5109

    # 服务器配置
    HOST = "0.0.0.0"
    RELOAD = False

    # 应用信息
    APP_NAME = "暨阳湖大酒店传菜管理系统"
    APP_VERSION = "1.0.0"

    @classmethod
    def get_port(cls):
        """获取服务器端口"""
        return cls.DEFAULT_PORT

    @classmethod
    def get_host(cls):
        """获取服务器主机"""
        return cls.HOST

    @classmethod
    def validate_port(cls, port):
        """验证端口号是否有效"""
        try:
            port_num = int(port)
            return 1024 <= port_num <= 65535
        except (ValueError, TypeError):
            return False

    @classmethod
    def get_app_info(cls):
        """获取应用信息"""
        return {
            "name": cls.APP_NAME,
            "version": cls.APP_VERSION,
            "port": cls.get_port(),
            "host": cls.get_host()
        }


# 创建配置实例
config = ServerConfig()
