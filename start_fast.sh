#!/bin/bash

echo "⚡ 启动暨阳湖大酒店传菜管理系统 - 高性能版本"
echo "=========================================="

cd backend_pure_python

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境并安装依赖
echo "安装依赖..."
PYTHONPATH=venv/lib/python3.13/site-packages pip3 install fastapi uvicorn sqlalchemy python-jose passlib python-multipart pydantic pydantic-settings jinja2 aiofiles bcrypt cryptography

# 检查数据库
if [ ! -f "paocai.db" ]; then
    echo "初始化数据库..."
    PYTHONPATH=venv/lib/python3.13/site-packages python3 init_db.py
fi

echo ""
echo "🚀 启动高性能服务器..."
echo "🌐 访问地址: http://localhost:8000"
echo "⚡ 性能优化: 多进程 + 连接池 + 缓存"
echo ""
echo "🔑 默认登录账号:"
echo "   管理员: admin / admin123"
echo "   经理: manager01 / manager123"
echo "   服务员: waiter01 / waiter123"
echo "   厨师长: chef01 / chef123"
echo ""
echo "按 Ctrl+C 停止服务"
echo "=========================================="

# 启动高性能服务器
PYTHONPATH=venv/lib/python3.13/site-packages python3 fast_start.py
