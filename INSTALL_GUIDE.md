# 🔧 安装指南 - 解决依赖问题

## 问题说明

您遇到的问题是因为某些Python包在Python 3.13上存在兼容性问题。我们提供了几种解决方案：

## 🚀 方案一：使用快速修复脚本（推荐）

### Linux/Mac:
```bash
chmod +x quick_fix.sh
./quick_fix.sh
```

### Windows:
```cmd
quick_fix.bat
```

## 🛠️ 方案二：手动安装

### 1. 进入项目目录
```bash
cd backend_pure_python
```

### 2. 删除旧的虚拟环境（如果存在）
```bash
# Linux/Mac
rm -rf venv

# Windows
rmdir /s /q venv
```

### 3. 创建新的虚拟环境
```bash
python3 -m venv venv  # Linux/Mac
python -m venv venv   # Windows
```

### 4. 激活虚拟环境
```bash
# Linux/Mac
source venv/bin/activate

# Windows
venv\Scripts\activate
```

### 5. 升级pip
```bash
pip install --upgrade pip
```

### 6. 逐个安装依赖
```bash
pip install fastapi
pip install "uvicorn[standard]"
pip install sqlalchemy
pip install "python-jose[cryptography]"
pip install "passlib[bcrypt]"
pip install python-multipart
pip install pydantic
pip install pydantic-settings
pip install jinja2
pip install aiofiles
```

### 7. 初始化数据库
```bash
python init_db.py
```

### 8. 启动服务
```bash
python main.py
```

## 🔍 方案三：使用较低版本的Python

如果上述方案仍有问题，建议使用Python 3.9-3.11版本：

### 1. 安装Python 3.11
- **macOS**: `brew install python@3.11`
- **Ubuntu**: `sudo apt install python3.11 python3.11-venv`
- **Windows**: 从 https://python.org 下载安装

### 2. 使用指定版本创建虚拟环境
```bash
python3.11 -m venv venv  # Linux/Mac
python3.11 -m venv venv  # Windows (如果安装了多个版本)
```

### 3. 按照方案二的步骤继续

## 🐳 方案四：使用Docker（最稳定）

如果您安装了Docker，可以使用容器化部署：

### 1. 创建Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装依赖
RUN pip install --upgrade pip
RUN pip install fastapi "uvicorn[standard]" sqlalchemy "python-jose[cryptography]" "passlib[bcrypt]" python-multipart pydantic pydantic-settings jinja2 aiofiles

# 复制代码
COPY . .

# 初始化数据库
RUN python init_db.py

# 暴露端口
EXPOSE 8000

# 启动服务
CMD ["python", "main.py"]
```

### 2. 构建和运行
```bash
cd backend_pure_python
docker build -t paocai-system .
docker run -p 8000:8000 paocai-system
```

## 📋 验证安装

安装完成后，访问 http://localhost:8000 应该能看到登录页面。

使用以下账号测试：
- 管理员: admin / admin123
- 经理: manager01 / manager123
- 服务员: waiter01 / waiter123
- 厨师长: chef01 / chef123

## ❓ 常见问题

### Q: 仍然提示模块不存在
A: 确保虚拟环境已激活，可以通过以下命令检查：
```bash
which python  # Linux/Mac
where python  # Windows
```
应该显示虚拟环境中的Python路径。

### Q: pip安装速度很慢
A: 使用国内镜像源：
```bash
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ fastapi
```

### Q: 权限问题
A: 
- Linux/Mac: 确保脚本有执行权限 `chmod +x script.sh`
- Windows: 以管理员身份运行命令提示符

### Q: 端口被占用
A: 修改 `main.py` 中的端口号：
```python
uvicorn.run(app, host="0.0.0.0", port=8001, reload=True)  # 改为8001
```

## 📞 获取帮助

如果仍有问题，请：
1. 检查Python版本：`python --version`
2. 检查pip版本：`pip --version`
3. 查看错误日志的完整信息
4. 尝试使用Docker方案

---

**记住：纯Python版本的优势就是简单！一旦依赖安装成功，系统就能稳定运行。** 🎉
