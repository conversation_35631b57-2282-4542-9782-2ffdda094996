from sqlalchemy import Column, Integer, String, Boolean, DateTime, Enum, Text, Numeric, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from core.database import Base


class DishCategory(str, enum.Enum):
    """菜品分类枚举"""
    COLD_DISH = "cold_dish"        # 凉菜
    HOT_DISH = "hot_dish"          # 热菜
    SOUP = "soup"                  # 汤羹
    STAPLE = "staple"              # 主食
    DESSERT = "dessert"            # 甜品
    BEVERAGE = "beverage"          # 饮品
    ALCOHOL = "alcohol"            # 酒类
    SEAFOOD = "seafood"            # 海鲜
    VEGETARIAN = "vegetarian"      # 素食
    SPECIALTY = "specialty"        # 招牌菜


class DishStatus(str, enum.Enum):
    """菜品状态枚举"""
    AVAILABLE = "available"        # 可供应
    UNAVAILABLE = "unavailable"    # 暂不供应
    SEASONAL = "seasonal"          # 时令菜
    LIMITED = "limited"            # 限量供应
    DISCONTINUED = "discontinued"  # 已停售


class SpicyLevel(str, enum.Enum):
    """辣度等级枚举"""
    NONE = "none"          # 不辣
    MILD = "mild"          # 微辣
    MEDIUM = "medium"      # 中辣
    HOT = "hot"            # 辣
    EXTRA_HOT = "extra_hot"  # 特辣


class CookingMethod(str, enum.Enum):
    """烹饪方式枚举"""
    STIR_FRY = "stir_fry"      # 炒
    STEAM = "steam"            # 蒸
    BOIL = "boil"              # 煮
    BRAISE = "braise"          # 红烧
    GRILL = "grill"            # 烤
    DEEP_FRY = "deep_fry"      # 炸
    COLD_MIX = "cold_mix"      # 凉拌
    STEW = "stew"              # 炖
    ROAST = "roast"            # 烘烤


class Dish(Base):
    """菜品模型"""
    __tablename__ = "dishes"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 基本信息
    name = Column(String(100), nullable=False, index=True, comment="菜品名称")
    english_name = Column(String(200), nullable=True, comment="英文名称")
    code = Column(String(50), unique=True, index=True, nullable=True, comment="菜品编码")
    category = Column(Enum(DishCategory), nullable=False, comment="菜品分类")
    status = Column(Enum(DishStatus), nullable=False, default=DishStatus.AVAILABLE, comment="状态")
    
    # 描述信息
    description = Column(Text, nullable=True, comment="菜品描述")
    ingredients = Column(Text, nullable=True, comment="主要食材")
    allergens = Column(Text, nullable=True, comment="过敏原信息")
    
    # 价格信息
    price = Column(Numeric(10, 2), nullable=False, comment="价格")
    cost = Column(Numeric(10, 2), nullable=True, comment="成本")
    market_price = Column(Numeric(10, 2), nullable=True, comment="市场价")
    member_price = Column(Numeric(10, 2), nullable=True, comment="会员价")
    
    # 规格信息
    unit = Column(String(20), nullable=False, default="份", comment="单位")
    serving_size = Column(String(50), nullable=True, comment="规格说明")
    weight = Column(Numeric(8, 2), nullable=True, comment="重量(克)")
    
    # 口味特征
    spicy_level = Column(Enum(SpicyLevel), nullable=False, default=SpicyLevel.NONE, comment="辣度")
    cooking_method = Column(Enum(CookingMethod), nullable=True, comment="烹饪方式")
    taste_tags = Column(Text, nullable=True, comment="口味标签(JSON)")
    
    # 制作信息
    prep_time = Column(Integer, nullable=True, comment="准备时间(分钟)")
    cook_time = Column(Integer, nullable=True, comment="烹饪时间(分钟)")
    difficulty = Column(Integer, nullable=True, comment="制作难度(1-5)")
    kitchen_station = Column(String(50), nullable=True, comment="制作工位")
    
    # 营养信息
    calories = Column(Integer, nullable=True, comment="卡路里")
    protein = Column(Numeric(8, 2), nullable=True, comment="蛋白质(克)")
    fat = Column(Numeric(8, 2), nullable=True, comment="脂肪(克)")
    carbs = Column(Numeric(8, 2), nullable=True, comment="碳水化合物(克)")
    
    # 图片和媒体
    image_url = Column(String(255), nullable=True, comment="菜品图片URL")
    thumbnail_url = Column(String(255), nullable=True, comment="缩略图URL")
    video_url = Column(String(255), nullable=True, comment="视频URL")
    
    # 销售信息
    is_recommended = Column(Boolean, default=False, comment="是否推荐")
    is_signature = Column(Boolean, default=False, comment="是否招牌菜")
    is_new = Column(Boolean, default=False, comment="是否新品")
    is_popular = Column(Boolean, default=False, comment="是否热门")
    
    # 供应限制
    daily_limit = Column(Integer, nullable=True, comment="每日限量")
    min_order_quantity = Column(Integer, default=1, comment="最小起订量")
    max_order_quantity = Column(Integer, nullable=True, comment="最大订购量")
    
    # 时间限制
    available_start_time = Column(String(10), nullable=True, comment="供应开始时间")
    available_end_time = Column(String(10), nullable=True, comment="供应结束时间")
    
    # 统计信息
    sales_count = Column(Integer, default=0, comment="销售数量")
    rating = Column(Numeric(3, 2), nullable=True, comment="评分")
    rating_count = Column(Integer, default=0, comment="评分次数")
    
    # 状态标记
    is_active = Column(Boolean, default=True, comment="是否启用")
    sort_order = Column(Integer, default=0, comment="排序")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    order_items = relationship("OrderItem", back_populates="dish")
    
    def __repr__(self):
        return f"<Dish(id={self.id}, name='{self.name}', price={self.price})>"
    
    @property
    def is_available_now(self) -> bool:
        """当前是否可供应"""
        if not self.is_active or self.status != DishStatus.AVAILABLE:
            return False
        
        # 检查时间限制
        if self.available_start_time and self.available_end_time:
            from datetime import datetime, timezone, timedelta
            CHINA_TZ = timezone(timedelta(hours=8))
            now = datetime.now(CHINA_TZ).time()
            start_time = datetime.strptime(self.available_start_time, "%H:%M").time()
            end_time = datetime.strptime(self.available_end_time, "%H:%M").time()
            
            if start_time <= end_time:
                return start_time <= now <= end_time
            else:  # 跨天的情况
                return now >= start_time or now <= end_time
        
        return True
    
    @property
    def profit_margin(self) -> float:
        """利润率"""
        if self.cost and self.cost > 0:
            return float((self.price - self.cost) / self.price * 100)
        return 0.0
    
    @property
    def total_time(self) -> int:
        """总制作时间"""
        prep = self.prep_time or 0
        cook = self.cook_time or 0
        return prep + cook
