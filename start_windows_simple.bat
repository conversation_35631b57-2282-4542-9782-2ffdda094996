@echo off
chcp 65001 >nul 2>&1
title Jiyang Lake Hotel Management System

echo ==========================================
echo Jiyang Lake Hotel Management System
echo Simple Windows Startup Script
echo ==========================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and add it to PATH
    pause
    exit /b 1
)

echo Python environment detected successfully
echo.

REM Change to backend directory
if not exist "backend_pure_python" (
    echo ERROR: backend_pure_python directory not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

cd backend_pure_python

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating Python virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ERROR: Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment
    pause
    exit /b 1
)

REM Install/update dependencies
echo Installing dependencies...
pip install --upgrade pip >nul 2>&1
pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

REM Check if database exists, if not initialize it
if not exist "paocai.db" (
    echo Database not found, initializing...
    python init_db.py
    if errorlevel 1 (
        echo ERROR: Failed to initialize database
        pause
        exit /b 1
    )
    echo Database initialized successfully
)

REM Start the application
echo.
echo ==========================================
echo Starting Jiyang Lake Hotel Management System
echo ==========================================
echo.
echo System will start on: http://localhost:5109
echo API Documentation: http://localhost:5109/docs
echo.
echo Press Ctrl+C to stop the service
echo.

python main.py

REM If we get here, the server has stopped
echo.
echo Server stopped. Press any key to exit...
pause >nul
