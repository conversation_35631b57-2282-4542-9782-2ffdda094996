<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>厨房大屏 - 暨阳湖大酒店传菜管理系统</title>
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/jiyang-enhanced.css" rel="stylesheet">
    <style>
        body {
            background-color: #000000;
            color: #ffff00;
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: auto;
            font-weight: bold;
            font-size: 16px;
        }
        
        .kitchen-header {
            background: linear-gradient(135deg, #1a1a1a, #333333);
            color: #ffff00;
            padding: 10px 20px;
            border-bottom: 3px solid #ffff00;
            position: sticky;
            top: 0;
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .header-title {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.3rem;
            font-weight: bold;
            color: #ffff00;
            text-shadow: 0 0 8px rgba(255, 255, 0, 0.4);
            white-space: nowrap;
        }

        .header-time {
            font-size: 2.5rem;
            font-weight: bold;
            color: #ffff00;
            text-align: center;
            text-shadow: 0 0 10px rgba(255, 255, 0, 0.5);
            letter-spacing: 2px;
        }

        /* 小屏幕适配 */
        @media (max-width: 768px) {
            .header-title {
                font-size: 1rem;
                left: 10px;
            }

            .header-time {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            .header-title {
                display: none; /* 超小屏幕隐藏标题 */
            }

            .header-time {
                font-size: 1.8rem;
            }
        }

        .header-controls {
            position: absolute;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .fullscreen-btn, .back-btn {
            background: rgba(255, 255, 0, 0.8);
            color: #000;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            font-weight: bold;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .fullscreen-btn:hover, .back-btn:hover {
            background: #ffff00;
            transform: scale(1.05);
        }
        
        .rooms-container {
            padding: 15px;
            display: flex;
            flex-direction: row;
            gap: 15px;
            height: calc(100vh - 100px);
            overflow-x: auto;
            overflow-y: hidden;
            justify-content: flex-start;
            align-items: stretch;
        }

        .room-column {
            background: #1a1a1a;
            border-radius: 12px;
            padding: 15px;
            border: 2px solid #ffff00;
            width: calc(20% - 12px);
            min-width: 280px;
            max-width: 350px;
            height: calc(100vh - 140px);
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            flex-shrink: 0;
        }
        
        .room-column.not-served {
            background: #2a2a2a;
            border-color: #666666;
            opacity: 0.7;
        }
        
        .room-column.served {
            background: #1a1a1a;
            border-color: #00ff00;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
        }
        
        .room-header {
            margin-bottom: 10px;
            padding: 8px;
            background: rgba(255, 255, 0, 0.1);
            border-radius: 6px;
            border: 1px solid #ffff00;
            flex-shrink: 0;
        }

        .room-title {
            font-size: 1.2rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 6px;
            color: #ffff00;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .room-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
            margin-bottom: 5px;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 0.8rem;
            color: #fff;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .info-item i {
            color: #ffff00;
            font-size: 0.7rem;
            flex-shrink: 0;
        }

        .info-item.special {
            color: #ff6b6b;
        }
        
        .room-info {
            background: rgba(255, 255, 0, 0.05);
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 1rem;
            border: 1px solid #ffff00;
        }
        
        .dish-list {
            flex: 1;
            overflow: hidden;
            padding-right: 2px;
        }

        .dish-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: 8px 10px;
            margin: 2px;
            transition: all 0.3s ease;
            border: 1px solid transparent;
            display: inline-block;
            width: calc(50% - 4px);
            text-align: center;
            position: relative;
            box-sizing: border-box;
            height: auto;
            min-height: 35px;
            vertical-align: top;
        }

        /* 菜品标签容器 - 每行2个标签 */
        .dish-tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 2px;
            align-items: flex-start;
            justify-content: flex-start;
            width: 100%;
        }

        /* 未制作菜品 - 醒目显示 */
        .dish-item.pending {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            font-weight: bold;
            font-size: 0.9rem;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }

        /* 制作中菜品 - 中等醒目 */
        .dish-item.cooking {
            background: linear-gradient(135deg, #ffc107, #ffb300);
            color: #333;
            font-weight: 600;
            font-size: 0.85rem;
            box-shadow: 0 2px 6px rgba(255, 193, 7, 0.3);
        }

        /* 已完成菜品 - 淡化显示 */
        .dish-item.completed {
            background: rgba(108, 117, 125, 0.3);
            color: #6c757d;
            font-size: 0.8rem;
            font-weight: normal;
        }

        .dish-item.completed .dish-name {
            text-decoration: line-through;
            text-decoration-color: #6c757d;
            text-decoration-thickness: 1px;
            opacity: 0.8;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .dish-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .dish-name {
            font-size: 0.7rem;
            font-weight: bold;
            margin-bottom: 1px;
            color: #fff;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.2;
        }

        .dish-special {
            font-size: 0.55rem;
            color: #ff6b6b;
            margin-top: 1px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.1;
        }
        
        .dish-status {
            font-size: 0.9rem;
            padding: 3px 8px;
            border-radius: 15px;
            margin-top: 5px;
            display: inline-block;
        }
        
        .status-pending_cook {
            background: #ff4444;
            color: #fff;
        }
        
        .status-cooking {
            background: #ff8800;
            color: #fff;
        }
        
        .status-ready {
            background: #00aa00;
            color: #fff;
        }
        
        .status-served {
            background: #666666;
            color: #fff;
        }
        
        .color-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 15px;
        }
        
        .color-tag {
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            color: #000;
            font-weight: bold;
            border: 1px solid #000;
        }
        
        .status-info {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: #ffff00;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 0.9rem;
            border: 1px solid #ffff00;
        }
        
        .no-rooms {
            text-align: center;
            padding: 100px 20px;
            font-size: 2rem;
            color: #666;
        }
        
        /* 全屏模式样式 */
        .fullscreen-mode {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 9999;
            background: #000;
            overflow: auto;
        }
        
        .fullscreen-mode .kitchen-header {
            position: relative;
        }
        
        .fullscreen-mode .room-column {
            width: calc(25% - 20px);
            min-width: 300px;
        }
        
        @media (max-width: 1600px) {
            .fullscreen-mode .room-column {
                width: calc(33.333% - 20px);
            }
        }
        
        @media (max-width: 1200px) {
            .fullscreen-mode .room-column {
                width: calc(50% - 20px);
            }
        }
        
        @media (max-width: 800px) {
            .fullscreen-mode .room-column {
                width: calc(100% - 20px);
            }
        }

        /* 自动缩放功能 */
        .auto-scale {
            transform-origin: top left;
            transition: transform 0.3s ease;
        }

        /* 根据包厢数量自动调整大小 */
        .rooms-container[data-room-count="1"] .room-column {
            width: calc(100% - 40px);
        }

        .rooms-container[data-room-count="2"] .room-column {
            width: calc(50% - 20px);
        }

        .rooms-container[data-room-count="3"] .room-column {
            width: calc(33.333% - 20px);
        }

        .rooms-container[data-room-count="4"] .room-column {
            width: calc(25% - 20px);
        }

        .rooms-container[data-room-count="5"],
        .rooms-container[data-room-count="6"] .room-column {
            width: calc(20% - 20px);
            min-width: 280px;
        }

        .rooms-container[data-room-count="7"],
        .rooms-container[data-room-count="8"],
        .rooms-container[data-room-count="9"],
        .rooms-container[data-room-count="10"] .room-column {
            width: calc(16.666% - 20px);
            min-width: 250px;
        }

        /* 横排布局响应式设计 */
        @media (max-width: 1600px) {
            .room-column {
                min-width: 250px;
                width: calc(20% - 12px);
            }
        }

        @media (max-width: 1400px) {
            .room-column {
                min-width: 220px;
                width: calc(25% - 12px);
            }
        }

        @media (max-width: 1200px) {
            .room-column {
                min-width: 200px;
                width: calc(33.333% - 12px);
            }
        }

        /* 滚动条样式 */
        .rooms-container::-webkit-scrollbar {
            height: 8px;
        }

        .rooms-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .rooms-container::-webkit-scrollbar-thumb {
            background: #ffff00;
            border-radius: 4px;
        }

        .rooms-container::-webkit-scrollbar-thumb:hover {
            background: #fff;
        }

        .dish-list::-webkit-scrollbar {
            width: 4px;
        }

        .dish-list::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }

        .dish-list::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 0, 0.5);
            border-radius: 2px;
        }

        /* 分页显示样式 */
        .page-container {
            display: none;
        }

        .page-container.active {
            display: block;
        }

        .page-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: #ffff00;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 1rem;
            border: 1px solid #ffff00;
            z-index: 1000;
        }

        .auto-flip-indicator {
            position: fixed;
            bottom: 70px;
            right: 20px;
            background: rgba(255, 255, 0, 0.1);
            color: #ffff00;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 0.9rem;
            border: 1px solid #ffff00;
            z-index: 1000;
        }

        /* 指令弹窗样式 */
        .command-popup {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 193, 7, 0.95);
            color: #000;
            border: 3px solid #ffc107;
            border-radius: 10px;
            padding: 15px 20px;
            z-index: 9999;
            max-width: 400px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            animation: slideInRight 0.5s ease-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .command-popup .popup-header {
            font-weight: bold;
            font-size: 1.2rem;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .command-popup .popup-content {
            font-size: 1rem;
            margin-bottom: 10px;
        }

        .command-popup .popup-time {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 10px;
        }

        .command-popup .popup-actions {
            text-align: right;
        }

        .command-popup .btn {
            margin-left: 5px;
        }

        .command-popup .popup-status {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .command-popup .status-indicator {
            font-size: 0.8rem;
            color: #666;
            font-style: italic;
        }

        /* 用餐开始弹窗特殊样式 */
        .dining-start-popup {
            background: linear-gradient(135deg, #1a5f1a 0%, #2d4a2d 100%) !important;
            border: 3px solid #00ff00 !important;
            color: #00ff00 !important;
            box-shadow: 0 4px 20px rgba(0, 255, 0, 0.4) !important;
            animation: slideInRight 0.5s ease-out, pulse-green 2s infinite !important;
        }

        .dining-start-header {
            background: rgba(0, 255, 0, 0.1) !important;
            margin: -15px -20px 10px -20px !important;
            padding: 10px 20px !important;
            border-radius: 7px 7px 0 0 !important;
            border-bottom: 1px solid #00ff00 !important;
            color: #00ff00 !important;
        }

        .dining-start-info {
            font-size: 1.2rem !important;
            font-weight: bold !important;
            text-align: center !important;
            margin: 10px 0 !important;
            padding: 10px !important;
            background: rgba(0, 255, 0, 0.1) !important;
            border-radius: 4px !important;
            color: #00ff00 !important;
        }

        .dining-start-message {
            font-size: 1.1rem !important;
            font-weight: bold !important;
            text-align: center !important;
            color: #ffffff !important;
            background: rgba(0, 255, 0, 0.2) !important;
            padding: 8px !important;
            border-radius: 4px !important;
            margin: 10px 0 !important;
        }

        @keyframes pulse-green {
            0% { box-shadow: 0 4px 20px rgba(0, 255, 0, 0.4); }
            50% { box-shadow: 0 4px 30px rgba(0, 255, 0, 0.6); }
            100% { box-shadow: 0 4px 20px rgba(0, 255, 0, 0.4); }
        }

        /* 队列显示样式 */
        .queue-display {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 123, 255, 0.9);
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-weight: bold;
            z-index: 10000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            animation: fadeIn 0.3s ease-in;
        }

        .queue-display .playing-indicator {
            display: block;
            font-size: 0.8rem;
            margin-top: 5px;
            color: #ffeb3b;
            animation: pulse 1.5s infinite;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }
    </style>
</head>
<body>

    
    <div class="kitchen-header">
        <div class="header-title">暨阳湖大酒店传菜管理系统</div>
        <div class="header-time" id="currentTime"></div>
        <div class="header-controls">
            <a href="/kitchen" class="back-btn">
                <i class="bi bi-arrow-left"></i> 返回
            </a>
            <button class="fullscreen-btn" onclick="toggleFullscreen()">
                <i class="bi bi-fullscreen"></i> 全屏
            </button>
        </div>
    </div>
    
    <div class="status-info">
        <i class="bi bi-info-circle"></i>
        厨房大屏 | 实时显示 | 自动刷新 | 仅供信息查看
    </div>
    
    {% if orders_by_room %}
    <!-- 分页容器 -->
    <div id="pagesContainer">
        {% set rooms_per_page = 5 %}
        {% set total_rooms = orders_by_room|length %}
        {% set total_pages = ((total_rooms - 1) // rooms_per_page) + 1 %}

        {% for page_num in range(total_pages) %}
        <div class="page-container {% if page_num == 0 %}active{% endif %}" data-page="{{ page_num }}">
            <div class="rooms-container">
                {% for room_name, room_data in orders_by_room.items() %}
                {% set room_index = loop.index0 %}
                {% if room_index >= page_num * rooms_per_page and room_index < (page_num + 1) * rooms_per_page %}
                {% if room_data is mapping %}
                    {% set dish_items = room_data.dish_items %}
                    {% set is_served = room_data.is_served %}
                    {% set can_operate = room_data.can_operate %}
                    {% set room_info = room_data.room_info %}
                {% else %}
                    {% set dish_items = room_data %}
                    {% set is_served = false %}
                    {% set can_operate = false %}
                    {% set room_info = {} %}
                {% endif %}
        
        <div class="room-column {% if is_served %}served{% else %}not-served{% endif %}">
            <div class="room-header">
                <h3 class="room-title">{{ room_name }}</h3>
                <div class="room-info">
                    <div class="info-item">
                        <i class="bi bi-people"></i>
                        <span>{{ room_info.guest_count or 0 }}人</span>
                    </div>
                    <div class="info-item">
                        <i class="bi bi-currency-yen"></i>
                        <span>{{ room_info.dining_standard or 0 }}元/桌</span>
                    </div>
                    {% if room_info.dining_start_time %}
                    <div class="info-item">
                        <i class="bi bi-clock"></i>
                        <span>开始用餐：{{ room_info.dining_start_time }}</span>
                    </div>
                    {% else %}
                    <div class="info-item">
                        <i class="bi bi-clock-history"></i>
                        <span>等待开始用餐</span>
                    </div>
                    {% endif %}
                    {% if room_info.special_requirements %}
                    <div class="info-item special">
                        <i class="bi bi-exclamation-triangle"></i>
                        <span>{{ room_info.special_requirements }}</span>
                    </div>
                    {% endif %}
                </div>
                {% if is_served %}
                <div style="font-size: 1rem; color: #00ff00;">✓ 使用中</div>
                {% else %}
                <div style="font-size: 1rem; color: #666;">○ 已预订</div>
                {% endif %}
            </div>

            {% if dish_items %}
            <div class="dish-list">
                <!-- 所有菜品统一显示 -->
                <div class="dish-tags-container">
                    {% for item in dish_items %}
                    <div class="dish-item {% if item.status.value == 'pending_cook' %}pending{% elif item.status.value == 'ready' %}completed{% endif %}"
                         id="dish-{{ item.id }}" title="{{ item.dish_name }}{% if item.special_requirements %} - {{ item.special_requirements }}{% endif %}">
                        <div class="dish-name">{{ item.dish_name }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% else %}
            <div class="text-center py-4" style="color: #666;">
                <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                <div class="mt-2">暂无菜品</div>
            </div>
            {% endif %}
        </div>
        {% endif %}
        {% endfor %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- 分页指示器 -->
    {% if total_pages > 1 %}
    <div class="page-indicator">
        <span id="currentPageIndicator">第 1 页 / 共 {{ total_pages }} 页</span>
    </div>
    <div class="auto-flip-indicator">
        <i class="bi bi-arrow-repeat"></i> 自动翻页: <span id="flipCountdown">8</span>s
    </div>
    {% endif %}
    {% else %}
    <div class="no-rooms">
        <i class="bi bi-house"></i><br>
        暂无预订包厢
    </div>
    {% endif %}

    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleTimeString('zh-CN', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeStr;
        }
        
        // 全屏切换防抖变量
        let fullscreenToggling = false;

        // 全屏切换
        function toggleFullscreen() {
            // 防抖机制，避免快速重复点击
            if (fullscreenToggling) {
                console.log('全屏切换中，请稍候...');
                return;
            }

            if (!checkFullscreenSupport()) {
                alert('您的浏览器不支持全屏功能');
                return;
            }

            fullscreenToggling = true;

            try {
                if (!getFullscreenElement()) {
                    // 进入全屏
                    requestFullscreen(document.documentElement).then(() => {
                        document.body.classList.add('fullscreen-mode');
                        localStorage.setItem('isFullscreen', 'true');
                        console.log('✅ 已进入全屏模式');
                        fullscreenToggling = false;
                    }).catch((error) => {
                        console.error('❌ 进入全屏失败:', error);
                        localStorage.removeItem('isFullscreen');
                        fullscreenToggling = false;
                        alert('进入全屏失败，请检查浏览器设置');
                    });
                } else {
                    // 退出全屏
                    exitFullscreen().then(() => {
                        document.body.classList.remove('fullscreen-mode');
                        localStorage.removeItem('isFullscreen');
                        console.log('✅ 已退出全屏模式');
                        fullscreenToggling = false;
                    }).catch((error) => {
                        console.error('❌ 退出全屏失败:', error);
                        fullscreenToggling = false;
                    });
                }
            } catch (error) {
                console.error('❌ 全屏切换异常:', error);
                fullscreenToggling = false;
            }

            // 安全超时重置
            setTimeout(() => {
                fullscreenToggling = false;
            }, 3000);
        }

        // 检查浏览器全屏API兼容性
        function checkFullscreenSupport() {
            return !!(document.documentElement.requestFullscreen ||
                     document.documentElement.webkitRequestFullscreen ||
                     document.documentElement.mozRequestFullScreen ||
                     document.documentElement.msRequestFullscreen);
        }

        // 跨浏览器全屏API封装
        function requestFullscreen(element) {
            if (element.requestFullscreen) {
                return element.requestFullscreen();
            } else if (element.webkitRequestFullscreen) {
                return element.webkitRequestFullscreen();
            } else if (element.mozRequestFullScreen) {
                return element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                return element.msRequestFullscreen();
            }
            return Promise.reject(new Error('不支持全屏API'));
        }

        function exitFullscreen() {
            if (document.exitFullscreen) {
                return document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                return document.webkitExitFullscreen();
            } else if (document.mozCancelFullScreen) {
                return document.mozCancelFullScreen();
            } else if (document.msExitFullscreen) {
                return document.msExitFullscreen();
            }
            return Promise.reject(new Error('不支持退出全屏API'));
        }

        function getFullscreenElement() {
            return document.fullscreenElement ||
                   document.webkitFullscreenElement ||
                   document.mozFullScreenElement ||
                   document.msFullscreenElement;
        }

        // 检查并恢复全屏状态
        function checkAndRestoreFullscreen() {
            if (localStorage.getItem('isFullscreen') === 'true') {
                if (!checkFullscreenSupport()) {
                    console.warn('⚠️ 浏览器不支持全屏API');
                    localStorage.removeItem('isFullscreen');
                    return;
                }

                // 延迟恢复全屏，确保页面完全加载
                setTimeout(() => {
                    if (!getFullscreenElement()) {
                        requestFullscreen(document.documentElement).then(() => {
                            document.body.classList.add('fullscreen-mode');
                            console.log('✅ 全屏状态已恢复');
                        }).catch((error) => {
                            console.error('❌ 恢复全屏失败:', error);
                            localStorage.removeItem('isFullscreen');
                        });
                    }
                }, 500);
            }
        }

        // 监听全屏状态变化 - 跨浏览器兼容
        function handleFullscreenChange() {
            if (!getFullscreenElement()) {
                document.body.classList.remove('fullscreen-mode');
                localStorage.removeItem('isFullscreen');
                console.log('📱 已退出全屏模式');
            } else {
                document.body.classList.add('fullscreen-mode');
                localStorage.setItem('isFullscreen', 'true');
                console.log('📺 已进入全屏模式');
            }
        }

        // 添加跨浏览器全屏事件监听器
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('mozfullscreenchange', handleFullscreenChange);
        document.addEventListener('MSFullscreenChange', handleFullscreenChange);
        
        // 厨房大屏仅作为信息显示，移除所有操作功能

        // 分页功能
        let currentPage = 0;
        let totalPages = document.querySelectorAll('.page-container').length;
        let flipCountdown = 8;
        let flipTimer;
        let countdownTimer;

        function showPage(pageIndex) {
            // 隐藏所有页面
            document.querySelectorAll('.page-container').forEach(page => {
                page.classList.remove('active');
            });

            // 显示指定页面
            const targetPage = document.querySelector(`[data-page="${pageIndex}"]`);
            if (targetPage) {
                targetPage.classList.add('active');
                currentPage = pageIndex;

                // 更新页面指示器
                const indicator = document.getElementById('currentPageIndicator');
                if (indicator) {
                    indicator.textContent = `第 ${pageIndex + 1} 页 / 共 ${totalPages} 页`;
                }
            }
        }

        function nextPage() {
            const nextPageIndex = (currentPage + 1) % totalPages;
            showPage(nextPageIndex);
            resetFlipCountdown();
        }

        function resetFlipCountdown() {
            flipCountdown = 8;
            updateCountdownDisplay();
        }

        function updateCountdownDisplay() {
            const countdownElement = document.getElementById('flipCountdown');
            if (countdownElement) {
                countdownElement.textContent = flipCountdown;
            }
        }

        function startAutoFlip() {
            if (totalPages <= 1) return;

            // 倒计时
            countdownTimer = setInterval(() => {
                flipCountdown--;
                updateCountdownDisplay();

                if (flipCountdown <= 0) {
                    nextPage();
                }
            }, 1000);
        }

        // 动态响应式布局计算
        function calculateRoomsPerPage() {
            const screenWidth = window.innerWidth;
            if (screenWidth >= 1920) return 7;
            if (screenWidth >= 1600) return 6;
            if (screenWidth >= 1400) return 5;
            if (screenWidth >= 1200) return 4;
            return 3;
        }

        // 重新计算布局
        function recalculateLayout() {
            const newRoomsPerPage = calculateRoomsPerPage();
            const totalRooms = {{ orders_by_room|length if orders_by_room else 0 }};

            if (totalRooms > 0) {
                // 重新计算分页
                const newTotalPages = Math.ceil(totalRooms / newRoomsPerPage);

                // 如果页数发生变化，重新加载页面以应用新布局
                if (newTotalPages !== totalPages) {
                    localStorage.setItem('needsLayoutUpdate', 'true');
                    if (document.fullscreenElement) {
                        localStorage.setItem('isFullscreen', 'true');
                    }
                    location.reload();
                }
            }
        }

        // 菜品显示区域优化 - 自适应字体大小
        function adjustFontSizeByDishCount() {
            document.querySelectorAll('.dish-list').forEach(dishList => {
                const dishCount = dishList.querySelectorAll('.dish-item').length;
                const availableHeight = dishList.offsetHeight;

                let fontSize, lineHeight, spacing;

                if (dishCount <= 5) {
                    fontSize = '1.0rem';
                    lineHeight = '1.3';
                    spacing = '4px';
                } else if (dishCount <= 10) {
                    fontSize = '0.9rem';
                    lineHeight = '1.2';
                    spacing = '3px';
                } else if (dishCount <= 15) {
                    fontSize = '0.8rem';
                    lineHeight = '1.1';
                    spacing = '2px';
                } else {
                    fontSize = '0.7rem';
                    lineHeight = '1.0';
                    spacing = '1px';
                    // 如果菜品太多，启用滚动
                    if (dishCount > 20) {
                        dishList.style.overflowY = 'auto';
                    }
                }

                // 应用样式
                dishList.querySelectorAll('.dish-name').forEach(dishName => {
                    dishName.style.fontSize = fontSize;
                    dishName.style.lineHeight = lineHeight;
                });

                dishList.querySelectorAll('.dish-item').forEach(dishItem => {
                    dishItem.style.marginBottom = spacing;
                });
            });
        }

        // 窗口大小变化监听器
        window.addEventListener('resize', recalculateLayout);

        // 初始化
        updateTime();
        setInterval(updateTime, 1000);

        // 检查并恢复全屏状态
        checkAndRestoreFullscreen();

        // 调整菜品字体大小
        adjustFontSizeByDishCount();

        // 启动自动翻页
        if (totalPages > 1) {
            startAutoFlip();
        }

        // 语音播报队列管理
        let voiceQueue = [];
        let isPlaying = false;
        let processedCommands = new Set(); // 记录已处理的指令ID
        let processedNotifications = new Set(); // 记录已处理的菜品完成通知ID

        // 检查新指令
        function checkNewCommands() {
            fetch('/api/waiter-actions/latest')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.actions && data.actions.length > 0) {
                        // 按时间戳排序（精确到毫秒）
                        const sortedActions = data.actions.sort((a, b) =>
                            new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
                        );

                        sortedActions.forEach(action => {
                            if (!action.is_processed && !processedCommands.has(action.id)) {
                                addToVoiceQueue(action);
                                showCommandPopup(action);
                                processedCommands.add(action.id);
                            }
                        });

                        // 更新队列显示
                        updateQueueDisplay();

                        // 开始播报队列
                        if (!isPlaying) {
                            processVoiceQueue();
                        }
                    }
                })
                .catch(error => {
                    console.error('检查指令失败:', error);
                });
        }

        // 添加到语音播报队列
        function addToVoiceQueue(action) {
            let voiceMessage;

            // 处理各种指令类型，确保显示中文
            if (action.action_type === 'dining_start') {
                const guestCount = action.action_content || '未知';
                voiceMessage = `${action.room_number}包厢 ${guestCount}人 起菜`;
            } else if (action.action_type === 'dining_end') {
                voiceMessage = `${action.room_number}包厢 结束用餐`;
            } else if (action.action_type === 'urge_dish') {
                voiceMessage = `${action.room_number}包厢 催菜`;
            } else if (action.action_type === 'serve_dish') {
                voiceMessage = `${action.room_number}包厢 上菜`;
            } else if (action.action_type === 'clean_table') {
                voiceMessage = `${action.room_number}包厢 收脏餐`;
            } else {
                // 使用中文显示名称，如果有手工输入内容则包含
                const displayName = action.action_type_display || action.action_type;
                if (action.action_content && action.action_content.trim()) {
                    voiceMessage = `${action.room_number}包厢 ${displayName} ${action.action_content}`;
                } else {
                    voiceMessage = `${action.room_number}包厢 ${displayName}`;
                }
            }

            voiceQueue.push({
                id: action.id,
                message: voiceMessage,
                action: action,
                timestamp: new Date(action.created_at).getTime()
            });
        }

        // 更新队列显示
        function updateQueueDisplay() {
            const queueCount = voiceQueue.length;
            let queueDisplay = document.getElementById('queueDisplay');

            if (!queueDisplay) {
                queueDisplay = document.createElement('div');
                queueDisplay.id = 'queueDisplay';
                queueDisplay.className = 'queue-display';
                document.body.appendChild(queueDisplay);
            }

            if (queueCount > 0) {
                queueDisplay.innerHTML = `
                    <i class="bi bi-megaphone"></i>
                    待播报：${queueCount}条
                    ${isPlaying ? '<span class="playing-indicator">播报中...</span>' : ''}
                `;
                queueDisplay.style.display = 'block';
            } else {
                queueDisplay.style.display = 'none';
            }
        }

        // 处理语音播报队列
        async function processVoiceQueue() {
            if (isPlaying || voiceQueue.length === 0) {
                return;
            }

            isPlaying = true;
            updateQueueDisplay();

            while (voiceQueue.length > 0) {
                const voiceItem = voiceQueue.shift();
                await playVoiceMessage(voiceItem);
                updateQueueDisplay();

                // 播报完成后自动清除弹窗
                setTimeout(() => {
                    removeCommandPopup(voiceItem.id);
                }, 1000);
            }

            isPlaying = false;
            updateQueueDisplay();
        }

        // 播放语音消息（播报2次，间隔3-5秒）
        async function playVoiceMessage(voiceItem) {
            const message = voiceItem.message;

            for (let i = 0; i < 2; i++) {
                if ('speechSynthesis' in window) {
                    await new Promise((resolve) => {
                        const utterance = new SpeechSynthesisUtterance(message);
                        utterance.lang = 'zh-CN';
                        utterance.rate = 0.8;
                        utterance.volume = 1.0;
                        utterance.pitch = 1.0;

                        utterance.onend = () => {
                            resolve();
                        };

                        utterance.onerror = () => {
                            resolve();
                        };

                        speechSynthesis.speak(utterance);
                    });

                    // 两次播报之间间隔4秒
                    if (i === 0) {
                        await new Promise(resolve => setTimeout(resolve, 4000));
                    }
                } else {
                    // 如果不支持语音，等待相应时间
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    if (i === 0) {
                        await new Promise(resolve => setTimeout(resolve, 4000));
                    }
                }
            }

            console.log(`✅ 语音播报完成: ${message}`);
        }

        // 显示指令弹窗
        function showCommandPopup(action) {
            // 检查是否已存在相同ID的弹窗
            const existingPopup = document.querySelector(`[data-action-id="${action.id}"]`);
            if (existingPopup) {
                return; // 已存在，不重复创建
            }

            // 特殊处理用餐开始指令
            let popupContent;
            let popupClass = 'command-popup';

            if (action.action_type === 'dining_start') {
                popupClass += ' dining-start-popup';
                const guestCount = action.action_content || '未知';
                popupContent = `
                    <div class="popup-header dining-start-header">
                        <span><i class="bi bi-play-circle"></i> ${action.room_number}包厢 用餐开始</span>
                        <button type="button" class="btn-close" onclick="removeCommandPopup(${action.id})"></button>
                    </div>
                    <div class="popup-content">
                        <div class="dining-start-info">
                            <i class="bi bi-people"></i>
                            <strong>用餐人数：${guestCount}人</strong>
                        </div>
                        <div class="dining-start-message">
                            ${action.room_number}包厢 ${guestCount}人 起菜
                        </div>
                    </div>
                `;
            } else {
                popupContent = `
                    <div class="popup-header">
                        <span><i class="bi bi-megaphone"></i> ${action.room_number}包厢指令</span>
                        <button type="button" class="btn-close" onclick="removeCommandPopup(${action.id})"></button>
                    </div>
                    <div class="popup-content">
                        <strong>${action.action_type_display}</strong>
                        ${action.action_content ? '<br><small>' + action.action_content + '</small>' : ''}
                    </div>
                `;
            }

            const popup = document.createElement('div');
            popup.className = popupClass;
            popup.setAttribute('data-action-id', action.id);
            popup.innerHTML = `
                ${popupContent}
                <div class="popup-time">
                    <i class="bi bi-clock"></i> ${new Date(action.created_at).toLocaleTimeString()}
                </div>
                <div class="popup-actions">
                    <button type="button" class="btn btn-sm btn-success" onclick="confirmCommand(${action.id}, this)">
                        <i class="bi bi-check"></i> 确认
                    </button>
                    <button type="button" class="btn btn-sm btn-secondary" onclick="removeCommandPopup(${action.id})">
                        <i class="bi bi-x"></i> 关闭
                    </button>
                </div>
                <div class="popup-status">
                    <span class="status-indicator">等待播报...</span>
                </div>
            `;

            document.body.appendChild(popup);

            // 用餐开始消息5秒后自动消失
            if (action.action_type === 'dining_start') {
                setTimeout(() => {
                    removeCommandPopup(action.id);
                }, 5000);
            }
        }

        // 移除指令弹窗
        function removeCommandPopup(actionId) {
            const popup = document.querySelector(`[data-action-id="${actionId}"]`);
            if (popup) {
                popup.remove();
            }
        }

        // 更新弹窗状态
        function updatePopupStatus(actionId, status) {
            const popup = document.querySelector(`[data-action-id="${actionId}"]`);
            if (popup) {
                const statusIndicator = popup.querySelector('.status-indicator');
                if (statusIndicator) {
                    statusIndicator.textContent = status;
                }
            }
        }

        // 确认指令
        function confirmCommand(actionId, button) {
            fetch('/kitchen/actions/' + actionId + '/processed', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            }).then(response => {
                if (response.ok) {
                    button.innerHTML = '<i class="bi bi-check-circle"></i> 已确认';
                    button.disabled = true;
                    button.className = 'btn btn-sm btn-outline-success';

                    setTimeout(() => {
                        button.parentElement.parentElement.remove();
                    }, 2000);
                } else {
                    alert('确认失败');
                }
            });
        }

        // 检查打荷员菜品完成通知
        function checkDishCompletionNotifications() {
            fetch('/api/kitchen/dish-completion-notifications', {
                credentials: 'include' // 包含cookies进行认证
            })
                .then(response => response.json())
                .then(data => {
                    console.log(`🔍 检查菜品完成通知:`, data);
                    if (data.success && data.notifications && data.notifications.length > 0) {
                        console.log(`📋 收到 ${data.notifications.length} 条菜品完成通知`);
                        data.notifications.forEach(notification => {
                            // 检查是否已经处理过这个通知
                            if (!processedNotifications.has(notification.id)) {
                                console.log(`🆕 新的菜品完成通知:`, notification);

                                // 显示Toast通知
                                showDishCompletionToast(notification);

                                // 语音播报
                                if ('speechSynthesis' in window) {
                                    const utterance = new SpeechSynthesisUtterance(notification.message);
                                    utterance.lang = 'zh-CN';
                                    speechSynthesis.speak(utterance);
                                }

                                // 标记为已处理
                                processedNotifications.add(notification.id);
                                console.log(`📢 打荷员菜品完成通知: ${notification.message}`);
                            } else {
                                console.log(`⏭️ 通知已处理过: ${notification.id}`);
                            }
                        });
                    } else {
                        console.log(`📭 暂无新的菜品完成通知`);
                    }
                })
                .catch(error => {
                    console.error('检查菜品完成通知失败:', error);
                });
        }

        // 显示菜品完成Toast通知
        function showDishCompletionToast(notification) {
            const toast = document.createElement('div');
            toast.className = 'toast-notification dish-completion-toast';
            toast.innerHTML = `
                <div class="toast-header">
                    <i class="bi bi-check-circle-fill"></i>
                    <strong>菜品完成</strong>
                </div>
                <div class="toast-body">
                    ${notification.message}
                </div>
            `;

            // 样式设置
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(40, 167, 69, 0.4);
                z-index: 9999;
                min-width: 300px;
                font-weight: 600;
                animation: slideInRight 0.3s ease-out;
                border-left: 4px solid #fff;
            `;

            document.body.appendChild(toast);

            // 4秒后自动移除
            setTimeout(() => {
                toast.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 4000);
        }

        // 自动刷新 - 在翻页的同时刷新数据，保持全屏状态
        setInterval(() => {
            // 检查并保存全屏状态
            if (getFullscreenElement()) {
                localStorage.setItem('isFullscreen', 'true');
            }
            location.reload();
        }, 40000);

        // 定期检查新指令
        setInterval(checkNewCommands, 5000);

        // 定期检查打荷员菜品完成通知
        setInterval(checkDishCompletionNotifications, 3000);
    </script>
</body>
</html>
