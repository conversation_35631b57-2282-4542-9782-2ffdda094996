{% extends "base.html" %}

{% block title %}工作台 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">工作台</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-refresh btn-sm" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i>
                刷新
            </button>
        </div>
    </div>
</div>

<!-- 欢迎信息 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="card-title mb-2">
                            <i class="bi bi-sun"></i>
                            欢迎回来，{{ user.full_name }}！
                        </h4>
                        <p class="card-text text-muted">
                            今天是 <span id="current-date"></span>，祝您工作愉快！
                        </p>
                        <small class="text-muted">
                            角色：{{ user.role.value }} | 部门：{{ user.department or '未设置' }}
                        </small>
                    </div>
                    <div class="col-md-4 text-end">
                        <i class="bi bi-trophy text-warning" style="font-size: 3rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            餐桌总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.total_tables }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-table text-primary" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            使用中餐桌
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.occupied_tables }} / {{ stats.total_tables }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people text-danger" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            今日订单
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.today_orders }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-cart text-success" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            上座率
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.occupancy_rate }}%
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-graph-up text-warning" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-lightning"></i>
                    快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    {% if user.has_permission('order.create') %}
                    <div class="col-md-3 col-sm-6">
                        <a href="/orders/create" class="btn btn-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-plus-circle mb-2" style="font-size: 1.5rem;"></i>
                            <span>新建订单</span>
                        </a>
                    </div>
                    {% endif %}

                    {% if user.has_permission('table.view') %}
                    <div class="col-md-3 col-sm-6">
                        <a href="/tables" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-table mb-2" style="font-size: 1.5rem;"></i>
                            <span>餐桌管理</span>
                        </a>
                    </div>
                    {% endif %}

                    {% if user.has_permission('order.view') %}
                    <div class="col-md-3 col-sm-6">
                        <a href="/orders" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-cart mb-2" style="font-size: 1.5rem;"></i>
                            <span>订单管理</span>
                        </a>
                    </div>
                    {% endif %}

                    {% if user.has_permission('kitchen.view') %}
                    <div class="col-md-3 col-sm-6">
                        <a href="/kitchen" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-fire mb-2" style="font-size: 1.5rem;"></i>
                            <span>厨房操作</span>
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 最近订单 -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clock-history"></i>
                    最近订单
                </h6>
                <a href="/orders" class="btn btn-sm btn-primary">查看全部</a>
            </div>
            <div class="card-body">
                {% if recent_orders %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>餐桌</th>
                                <th>客人数</th>
                                <th>状态</th>
                                <th>金额</th>
                                <th>时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in recent_orders %}
                            <tr>
                                <td>
                                    <strong>{{ order.order_number }}</strong>
                                </td>
                                <td>
                                    {% if order.table %}
                                    <span class="badge bg-info">{{ order.table.number }}</span>
                                    {% else %}
                                    <span class="text-muted">外带</span>
                                    {% endif %}
                                </td>
                                <td>{{ order.guest_count }}人</td>
                                <td>
                                    <span class="badge bg-{% if order.status.value == 'completed' %}success{% elif order.status.value == 'cancelled' %}danger{% elif order.status.value == 'in_progress' %}warning{% elif order.status.value == 'confirmed' %}info{% else %}secondary{% endif %}">
                                        {% if order.status.value == 'draft' %}草稿
                                        {% elif order.status.value == 'confirmed' %}已确认
                                        {% elif order.status.value == 'in_progress' %}进行中
                                        {% elif order.status.value == 'completed' %}已完成
                                        {% elif order.status.value == 'cancelled' %}已取消
                                        {% elif order.status.value == 'pending_start' %}待开始
                                        {% elif order.status.value == 'serving' %}上菜中
                                        {% else %}{{ order.status.value }}
                                        {% endif %}
                                    </span>
                                </td>
                                <td>¥{{ "%.2f"|format(order.total_amount) }}</td>
                                <td>
                                    <small class="text-muted">
                                        {{ order.created_at.strftime('%H:%M') }}
                                    </small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                    <p class="text-muted mt-2">暂无订单</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 系统状态和菜品统计 -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-activity"></i>
                    系统状态
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="text-success">
                                <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                                <small class="d-block mt-2">系统正常</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-info">
                                <i class="bi bi-wifi" style="font-size: 2rem;"></i>
                                <small class="d-block mt-2">连接正常</small>
                            </div>
                        </div>
                    </div>
                </div>

                <hr class="my-3">

                <div class="row text-center">
                    <div class="col-12">
                        <h6 class="text-muted mb-3">今日营业概况</h6>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <div class="h4 text-primary">{{ stats.total_dishes or 0 }}</div>
                            <small class="text-muted">总菜品数</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-success">{{ stats.available_dishes or 0 }}</div>
                        <small class="text-muted">可供应</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 显示当前日期
    document.getElementById('current-date').textContent = new Date().toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
    });
    
    // 自动刷新页面数据（每30秒）
    setInterval(function() {
        // 这里可以添加AJAX请求来更新数据
        // 暂时使用页面刷新
    }, 30000);
</script>
{% endblock %}
