"""
指令模板模型
用于配置服务员可以发送的指令集
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text
from sqlalchemy.ext.declarative import declarative_base
from core.database import Base
from datetime import datetime

class CommandTemplate(Base):
    """指令模板表"""
    __tablename__ = "command_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="指令名称")
    code = Column(String(50), nullable=False, unique=True, comment="指令代码")
    description = Column(Text, comment="指令描述")
    voice_text = Column(String(200), comment="语音播报文本")
    category = Column(String(50), default="general", comment="指令分类")
    is_active = Column(Boolean, default=True, comment="是否启用")
    allow_input = Column(Boolean, default=False, comment="是否允许手动输入")
    input_placeholder = Column(String(200), comment="输入提示文字")
    input_required = Column(Boolean, default=False, comment="是否必须输入")
    sort_order = Column(Integer, default=0, comment="排序")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    def __repr__(self):
        return f"<CommandTemplate(name='{self.name}', code='{self.code}')>"
    
    @property
    def display_name(self):
        """显示名称"""
        return self.name
    
    @property
    def voice_message(self):
        """语音消息"""
        return self.voice_text or self.name
