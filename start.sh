#!/bin/bash

# 暨阳湖大酒店传菜管理系统启动脚本

echo "=========================================="
echo "暨阳湖大酒店传菜管理系统"
echo "=========================================="

# 检查 Python 版本
echo "检查 Python 环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python 3 未安装，请先安装 Python 3.9+"
    exit 1
fi

# 检查 Node.js 版本
echo "检查 Node.js 环境..."
node --version
if [ $? -ne 0 ]; then
    echo "❌ Node.js 未安装，请先安装 Node.js 18+"
    exit 1
fi

# 检查 PostgreSQL
echo "检查 PostgreSQL 连接..."
pg_isready -h localhost -p 5432
if [ $? -ne 0 ]; then
    echo "⚠️  PostgreSQL 未运行，请确保 PostgreSQL 服务已启动"
    echo "   或修改 backend/core/config.py 中的数据库配置"
fi

echo ""
echo "选择启动模式："
echo "1. 完整启动 (后端 + 前端)"
echo "2. 仅启动后端"
echo "3. 仅启动前端"
echo "4. 初始化数据库"
echo "5. 安装依赖"
read -p "请选择 (1-5): " choice

case $choice in
    1)
        echo "启动完整系统..."
        
        # 启动后端
        echo "启动后端服务..."
        cd backend
        python3 -m venv venv 2>/dev/null || true
        source venv/bin/activate
        pip install -r requirements.txt
        python init_db.py
        uvicorn main:socket_app --reload --host 0.0.0.0 --port 8000 &
        BACKEND_PID=$!
        cd ..
        
        # 等待后端启动
        sleep 5
        
        # 启动前端
        echo "启动前端服务..."
        cd frontend
        npm install
        npm run dev &
        FRONTEND_PID=$!
        cd ..
        
        echo ""
        echo "✅ 系统启动成功！"
        echo "📱 前端地址: http://localhost:3000"
        echo "🔧 后端地址: http://localhost:8000"
        echo "📚 API 文档: http://localhost:8000/docs"
        echo ""
        echo "按 Ctrl+C 停止服务"
        
        # 等待用户中断
        trap "echo '正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit" INT
        wait
        ;;
        
    2)
        echo "启动后端服务..."
        cd backend
        python3 -m venv venv 2>/dev/null || true
        source venv/bin/activate
        pip install -r requirements.txt
        python init_db.py
        uvicorn main:socket_app --reload --host 0.0.0.0 --port 8000
        ;;
        
    3)
        echo "启动前端服务..."
        cd frontend
        npm install
        npm run dev
        ;;
        
    4)
        echo "初始化数据库..."
        cd backend
        python3 -m venv venv 2>/dev/null || true
        source venv/bin/activate
        pip install -r requirements.txt
        python init_db.py
        echo "✅ 数据库初始化完成"
        ;;
        
    5)
        echo "安装后端依赖..."
        cd backend
        python3 -m venv venv 2>/dev/null || true
        source venv/bin/activate
        pip install -r requirements.txt
        cd ..
        
        echo "安装前端依赖..."
        cd frontend
        npm install
        cd ..
        
        echo "✅ 依赖安装完成"
        ;;
        
    *)
        echo "无效选择"
        exit 1
        ;;
esac
