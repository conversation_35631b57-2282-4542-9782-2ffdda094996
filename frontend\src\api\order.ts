import { api } from './client';
import { Order, OrderItem, OrderStatus, OrderType } from '@/types';

export interface OrderCreateData {
  table_id?: number;
  order_type?: OrderType;
  customer_name?: string;
  customer_phone?: string;
  guest_count: number;
  reservation_time?: string;
  estimated_duration?: number;
  special_requests?: string;
  notes?: string;
  items: Array<{
    dish_id: number;
    quantity: number;
    special_requirements?: string;
  }>;
}

export interface OrderUpdateData {
  customer_name?: string;
  customer_phone?: string;
  guest_count?: number;
  reservation_time?: string;
  estimated_duration?: number;
  special_requests?: string;
  notes?: string;
  status?: OrderStatus;
}

export interface OrderItemUpdateData {
  quantity?: number;
  special_requirements?: string;
  status?: string;
}

export const orderApi = {
  // 获取订单列表
  getOrders: (params?: {
    page?: number;
    size?: number;
    status?: OrderStatus;
    order_type?: OrderType;
    table_id?: number;
    waiter_id?: number;
    date_from?: string;
    date_to?: string;
  }): Promise<Order[]> => {
    return api.get('/api/orders', { params });
  },
  
  // 创建订单
  createOrder: (data: OrderCreateData): Promise<Order> => {
    return api.post('/api/orders', data);
  },
  
  // 获取订单详情
  getOrder: (id: number): Promise<Order> => {
    return api.get(`/api/orders/${id}`);
  },
  
  // 更新订单信息
  updateOrder: (id: number, data: OrderUpdateData): Promise<Order> => {
    return api.put(`/api/orders/${id}`, data);
  },
  
  // 添加订单项
  addOrderItem: (orderId: number, data: {
    dish_id: number;
    quantity: number;
    special_requirements?: string;
  }): Promise<OrderItem> => {
    return api.post(`/api/orders/${orderId}/items`, data);
  },
  
  // 更新订单项
  updateOrderItem: (orderId: number, itemId: number, data: OrderItemUpdateData): Promise<OrderItem> => {
    return api.put(`/api/orders/${orderId}/items/${itemId}`, data);
  },
  
  // 删除订单项
  deleteOrderItem: (orderId: number, itemId: number): Promise<{ message: string }> => {
    return api.delete(`/api/orders/${orderId}/items/${itemId}`);
  },
  
  // 催菜
  rushDish: (orderId: number, itemId: number): Promise<{ message: string; rush_count: number }> => {
    return api.post(`/api/orders/${orderId}/items/${itemId}/rush`);
  },
};
