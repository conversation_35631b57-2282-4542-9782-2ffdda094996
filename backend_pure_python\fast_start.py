#!/usr/bin/env python3
"""
快速启动脚本 - 性能优化版本
"""

import uvicorn
from main import app

if __name__ == "__main__":
    print("🚀 启动暨阳湖大酒店传菜管理系统 (性能优化版)")
    print("🌐 访问地址: http://localhost:8000")
    print("⚡ 性能优化: 启用多进程、连接池、缓存")
    print("🔑 默认登录账号:")
    print("   管理员: admin / admin123")
    print("   经理: manager01 / manager123")
    print("   服务员: waiter01 / waiter123")
    print("   厨师长: chef01 / chef123")
    print("按 Ctrl+C 停止服务")
    print("=" * 50)
    
    # 性能优化配置
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        workers=2,  # 多进程
        reload=False,
        access_log=False,  # 关闭访问日志提升性能
        log_level="warning",  # 减少日志输出
        loop="uvloop" if hasattr(uvicorn, 'uvloop') else "asyncio"  # 使用更快的事件循环
    )
