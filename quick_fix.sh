#!/bin/bash

echo "🔧 快速修复依赖问题..."

cd backend_pure_python

# 删除旧的虚拟环境
if [ -d "venv" ]; then
    echo "删除旧的虚拟环境..."
    rm -rf venv
fi

# 创建新的虚拟环境
echo "创建新的虚拟环境..."
python3 -m venv venv

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 升级pip
echo "升级pip..."
pip install --upgrade pip

# 逐个安装核心依赖
echo "安装核心依赖..."
pip install fastapi
pip install "uvicorn[standard]"
pip install sqlalchemy
pip install "python-jose[cryptography]"
pip install "passlib[bcrypt]"
pip install python-multipart
pip install pydantic
pip install pydantic-settings
pip install jinja2
pip install aiofiles

echo "✅ 依赖安装完成"

# 初始化数据库
echo "初始化数据库..."
python init_db.py

echo "✅ 数据库初始化完成"

# 启动服务
echo "启动服务..."
echo ""
echo "✅ 系统启动成功！"
echo "🌐 访问地址: http://localhost:8000"
echo ""
echo "默认登录账号:"
echo "管理员: admin / admin123"
echo "经理: manager01 / manager123"
echo "服务员: waiter01 / waiter123"
echo "厨师长: chef01 / chef123"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

python main.py
