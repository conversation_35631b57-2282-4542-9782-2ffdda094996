#!/usr/bin/env python3
"""
验证暨阳湖大酒店传菜管理系统的界面优化和功能改进
"""
import os
import re
import requests
from pathlib import Path

def check_kitchen_display_optimization():
    """检查厨房大屏界面优化"""
    print("🖥️ 检查厨房大屏界面优化...")
    
    kitchen_template = "templates/kitchen_display_new.html"
    if not os.path.exists(kitchen_template):
        print("❌ 厨房大屏模板不存在")
        return False
        
    with open(kitchen_template, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查菜品标签优化
    required_elements = [
        "width: calc(50% - 4px)",  # 每行2个标签
        "header-time",  # 时间居中显示
        "header-controls",  # 控制按钮右上角
        "font-size: 2.5rem",  # 时间字体增大
        "结束用餐",  # 中文显示
        "起菜"  # 语音播报内容
    ]
    
    for element in required_elements:
        if element in content:
            print(f"✅ {element}")
        else:
            print(f"❌ 缺少元素: {element}")
            return False
    
    # 检查移除小图标
    if "bi bi-clock-fill" not in content and "bi bi-check-circle-fill" not in content:
        print("✅ 菜品标签小图标已移除")
    else:
        print("❌ 菜品标签小图标未完全移除")
        return False
    
    return True

def check_kitchen_operation_optimization():
    """检查厨房操作界面简化"""
    print("\n🔧 检查厨房操作界面简化...")
    
    kitchen_template = "templates/kitchen.html"
    if not os.path.exists(kitchen_template):
        print("❌ 厨房操作模板不存在")
        return False
        
    with open(kitchen_template, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查简化内容
    required_elements = [
        "开始时间: {{ item.order.dining_start_time.strftime('%H:%M:%S') }}",  # 修复时间显示
        "<th>状态</th>",  # 简化表格
        "<span class=\"badge bg-success\">已完成</span>"  # 状态显示
    ]
    
    for element in required_elements:
        if element in content:
            print(f"✅ {element}")
        else:
            print(f"❌ 缺少元素: {element}")
            return False
    
    # 检查移除的内容
    removed_elements = [
        "<th>数量</th>",  # 移除份数显示
        "<th>完成时间</th>"  # 移除完成时间列
    ]
    
    for element in removed_elements:
        if element not in content:
            print(f"✅ 已移除: {element}")
        else:
            print(f"❌ 未移除: {element}")
            return False
    
    return True

def check_waiter_interface_enhancement():
    """检查服务员界面功能增强"""
    print("\n📱 检查服务员界面功能增强...")
    
    waiter_template = "templates/waiter_menu.html"
    waiter_css = "static/css/waiter-mobile.css"
    
    if not os.path.exists(waiter_template) or not os.path.exists(waiter_css):
        print("❌ 服务员界面文件不存在")
        return False
        
    with open(waiter_template, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    with open(waiter_css, 'r', encoding='utf-8') as f:
        css_content = f.read()
    
    # 检查结束用餐按钮优化
    required_elements = [
        "end-dining-btn",  # 特殊样式类
        "btn-danger btn-lg",  # 醒目按钮
        "bi bi-stop-circle-fill",  # 图标
        "initAutoRefresh"  # 自动刷新配置
    ]
    
    for element in required_elements:
        if element in template_content:
            print(f"✅ 模板包含: {element}")
        else:
            print(f"❌ 模板缺少: {element}")
            return False
    
    # 检查CSS样式
    css_elements = [
        "end-dining-btn",
        "min-height: 60px",
        "font-size: 1.2rem",
        "animation: blink"
    ]
    
    for element in css_elements:
        if element in css_content:
            print(f"✅ CSS包含: {element}")
        else:
            print(f"❌ CSS缺少: {element}")
            return False
    
    return True

def check_system_config_functionality():
    """检查系统设置功能"""
    print("\n⚙️ 检查系统设置功能...")
    
    system_config_model = "models/system_config.py"
    system_settings_template = "templates/system_settings.html"
    
    if not os.path.exists(system_config_model) or not os.path.exists(system_settings_template):
        print("❌ 系统设置文件不存在")
        return False
        
    with open(system_config_model, 'r', encoding='utf-8') as f:
        model_content = f.read()
    
    with open(system_settings_template, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    # 检查模型
    model_elements = [
        "class SystemConfig",
        "waiter_auto_refresh_interval",
        "waiter_auto_refresh_enabled",
        "typed_value"
    ]
    
    for element in model_elements:
        if element in model_content:
            print(f"✅ 模型包含: {element}")
        else:
            print(f"❌ 模型缺少: {element}")
            return False
    
    # 检查模板
    template_elements = [
        "服务员界面设置",
        "自动刷新间隔",
        "厨房界面设置",
        "订单管理设置"
    ]
    
    for element in template_elements:
        if element in template_content:
            print(f"✅ 模板包含: {element}")
        else:
            print(f"❌ 模板缺少: {element}")
            return False
    
    return True

def check_order_management_optimization():
    """检查订单管理界面优化"""
    print("\n📋 检查订单管理界面优化...")
    
    orders_template = "templates/orders.html"
    if not os.path.exists(orders_template):
        print("❌ 订单管理模板不存在")
        return False
        
    with open(orders_template, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查表格优化
    required_elements = [
        "<th>人数</th>",  # 人数单独列
        "text-center",  # 人数居中显示
        "bi bi-eye",  # 查看图标
        "bi bi-arrow-repeat",  # 更换服务员图标
        "bi bi-stop-circle-fill",  # 强制结束图标
        "btn-group-vertical"  # 垂直按钮组
    ]
    
    for element in required_elements:
        if element in content:
            print(f"✅ {element}")
        else:
            print(f"❌ 缺少元素: {element}")
            return False
    
    return True

def check_backend_logic_optimization():
    """检查后端逻辑优化"""
    print("\n⚙️ 检查后端逻辑优化...")
    
    main_py_path = "main.py"
    if not os.path.exists(main_py_path):
        print("❌ 主程序文件不存在")
        return False
        
    with open(main_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查包厢-服务员关联逻辑
    required_elements = [
        "确保一个包厢只对应一个服务员",
        "确保一个服务员只负责一个包厢",
        "SystemConfig",
        "system-settings",
        "api/system-config"
    ]
    
    for element in required_elements:
        if element in content:
            print(f"✅ {element}")
        else:
            print(f"❌ 缺少元素: {element}")
            return False
    
    return True

def check_server_response():
    """检查服务器响应"""
    print("\n🌐 检查服务器响应...")
    
    try:
        # 检查登录页面
        response = requests.get("http://localhost:8001/login", timeout=5)
        if response.status_code == 200:
            print("✅ 登录页面正常访问")
        else:
            print(f"❌ 登录页面访问失败: {response.status_code}")
            return False
        
        # 检查CSS文件
        response = requests.get("http://localhost:8001/static/css/waiter-mobile.css", timeout=5)
        if response.status_code == 200:
            css_content = response.text
            if "end-dining-btn" in css_content:
                print("✅ 移动端CSS包含结束用餐按钮样式")
            else:
                print("❌ 移动端CSS缺少结束用餐按钮样式")
                return False
        else:
            print(f"❌ 移动端CSS访问失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 服务器连接失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🎯 暨阳湖大酒店传菜管理系统界面优化和功能改进验证")
    print("=" * 80)
    
    checks = [
        ("厨房大屏界面优化", check_kitchen_display_optimization),
        ("厨房操作界面简化", check_kitchen_operation_optimization),
        ("服务员界面功能增强", check_waiter_interface_enhancement),
        ("系统设置功能", check_system_config_functionality),
        ("订单管理界面优化", check_order_management_optimization),
        ("后端逻辑优化", check_backend_logic_optimization),
        ("服务器响应", check_server_response)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}检查:")
        try:
            if check_func():
                passed += 1
            else:
                print(f"❌ {check_name}检查失败")
        except Exception as e:
            print(f"❌ {check_name}检查出错: {e}")
    
    print("\n" + "=" * 80)
    print(f"🎉 验证完成: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("✅ 界面优化和功能改进全面完成！")
        print("\n🌟 优化亮点:")
        print("   • 厨房大屏菜品标签布局优化（每行2个）")
        print("   • 时间显示居中，控制按钮右上角")
        print("   • 服务员指令中文显示和语音播报")
        print("   • 厨房操作界面简化，移除冗余信息")
        print("   • 服务员结束用餐按钮醒目化")
        print("   • 系统设置功能，支持自动刷新配置")
        print("   • 包厢-服务员一对一关联逻辑")
        print("   • 订单管理界面人数单独列显示")
        print("   • 操作按钮图标和文字标签优化")
        return True
    else:
        print("❌ 界面优化和功能改进仍需完善")
        return False

if __name__ == "__main__":
    main()
