"""
语音播报管理器
负责管理语音播报的次数控制和状态跟踪
"""
from sqlalchemy.orm import Session
from models.voice_broadcast_log import VoiceBroadcastLog
from models.voice_config import VoiceConfig
from models.waiter_action import WaiterAction
from datetime import datetime
import asyncio
import logging

logger = logging.getLogger(__name__)


class VoiceBroadcastManager:
    """语音播报管理器"""
    
    def __init__(self, db: Session):
        self.db = db
        self._voice_config_cache = None
        self._cache_time = None
    
    def get_voice_config(self):
        """获取语音配置（带缓存）"""
        now = datetime.utcnow()
        
        # 缓存5分钟
        if (self._voice_config_cache is None or 
            self._cache_time is None or 
            (now - self._cache_time).seconds > 300):
            
            try:
                configs = self.db.query(VoiceConfig).filter(VoiceConfig.is_active == True).all()
                
                config = {}
                for c in configs:
                    if c.config_key == 'voice_enabled':
                        config['enabled'] = c.config_value.lower() == 'true'
                    elif c.config_key == 'voice_repeat_count':
                        config['repeat_count'] = int(c.config_value)
                    elif c.config_key == 'voice_repeat_interval':
                        config['repeat_interval'] = int(c.config_value)
                    elif c.config_key == 'voice_rate':
                        config['rate'] = float(c.config_value)
                    elif c.config_key == 'voice_volume':
                        config['volume'] = float(c.config_value)
                    elif c.config_key == 'voice_pitch':
                        config['pitch'] = float(c.config_value)
                
                # 设置默认值
                default_config = {
                    'enabled': True,
                    'repeat_count': 2,
                    'repeat_interval': 3,
                    'rate': 0.8,
                    'volume': 1.0,
                    'pitch': 1.0
                }
                
                for key, default_value in default_config.items():
                    if key not in config:
                        config[key] = default_value
                
                self._voice_config_cache = config
                self._cache_time = now
                
            except Exception as e:
                logger.error(f"获取语音配置失败: {e}")
                # 返回默认配置
                self._voice_config_cache = {
                    'enabled': True,
                    'repeat_count': 2,
                    'repeat_interval': 3,
                    'rate': 0.8,
                    'volume': 1.0,
                    'pitch': 1.0
                }
                self._cache_time = now
        
        return self._voice_config_cache
    
    def create_broadcast_log(self, waiter_action_id: int, message: str) -> VoiceBroadcastLog:
        """为指令创建播报记录"""
        try:
            config = self.get_voice_config()
            max_count = config.get('repeat_count', 2)
            
            # 检查是否已存在播报记录
            existing_log = self.db.query(VoiceBroadcastLog).filter(
                VoiceBroadcastLog.waiter_action_id == waiter_action_id
            ).first()
            
            if existing_log:
                return existing_log
            
            # 创建新的播报记录
            broadcast_log = VoiceBroadcastLog(
                waiter_action_id=waiter_action_id,
                broadcast_message=message,
                max_broadcast_count=max_count,
                broadcast_count=0,
                is_completed=False
            )
            
            self.db.add(broadcast_log)
            self.db.commit()
            
            logger.info(f"创建播报记录: 指令ID={waiter_action_id}, 最大次数={max_count}")
            return broadcast_log
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建播报记录失败: {e}")
            raise
    
    def can_broadcast(self, waiter_action_id: int) -> bool:
        """检查指令是否可以播报"""
        try:
            config = self.get_voice_config()
            if not config.get('enabled', True):
                return False
            
            broadcast_log = self.db.query(VoiceBroadcastLog).filter(
                VoiceBroadcastLog.waiter_action_id == waiter_action_id
            ).first()
            
            if not broadcast_log:
                return True  # 没有记录，可以播报
            
            return broadcast_log.can_broadcast()
            
        except Exception as e:
            logger.error(f"检查播报权限失败: {e}")
            return False
    
    def record_broadcast(self, waiter_action_id: int) -> bool:
        """记录一次播报"""
        try:
            broadcast_log = self.db.query(VoiceBroadcastLog).filter(
                VoiceBroadcastLog.waiter_action_id == waiter_action_id
            ).first()
            
            if not broadcast_log:
                logger.warning(f"未找到播报记录: 指令ID={waiter_action_id}")
                return False
            
            broadcast_log.record_broadcast()
            self.db.commit()
            
            logger.info(f"记录播报: 指令ID={waiter_action_id}, 次数={broadcast_log.broadcast_count}/{broadcast_log.max_broadcast_count}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"记录播报失败: {e}")
            return False
    
    def get_broadcast_status(self, waiter_action_id: int) -> dict:
        """获取播报状态"""
        try:
            broadcast_log = self.db.query(VoiceBroadcastLog).filter(
                VoiceBroadcastLog.waiter_action_id == waiter_action_id
            ).first()
            
            if not broadcast_log:
                return {
                    'exists': False,
                    'can_broadcast': True,
                    'broadcast_count': 0,
                    'max_count': self.get_voice_config().get('repeat_count', 2),
                    'is_completed': False
                }
            
            return {
                'exists': True,
                'can_broadcast': broadcast_log.can_broadcast(),
                'broadcast_count': broadcast_log.broadcast_count,
                'max_count': broadcast_log.max_broadcast_count,
                'is_completed': broadcast_log.is_completed,
                'first_broadcast_at': broadcast_log.first_broadcast_at,
                'last_broadcast_at': broadcast_log.last_broadcast_at
            }
            
        except Exception as e:
            logger.error(f"获取播报状态失败: {e}")
            return {
                'exists': False,
                'can_broadcast': False,
                'broadcast_count': 0,
                'max_count': 0,
                'is_completed': True,
                'error': str(e)
            }
    
    def reset_broadcast(self, waiter_action_id: int) -> bool:
        """重置播报状态（用于重新播报）"""
        try:
            broadcast_log = self.db.query(VoiceBroadcastLog).filter(
                VoiceBroadcastLog.waiter_action_id == waiter_action_id
            ).first()
            
            if not broadcast_log:
                logger.warning(f"未找到播报记录: 指令ID={waiter_action_id}")
                return False
            
            broadcast_log.reset_broadcast()
            self.db.commit()
            
            logger.info(f"重置播报状态: 指令ID={waiter_action_id}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"重置播报状态失败: {e}")
            return False
    
    def cleanup_old_logs(self, days: int = 7):
        """清理旧的播报记录"""
        try:
            from datetime import timedelta
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            deleted_count = self.db.query(VoiceBroadcastLog).filter(
                VoiceBroadcastLog.created_at < cutoff_date
            ).delete()
            
            self.db.commit()
            
            if deleted_count > 0:
                logger.info(f"清理了 {deleted_count} 条旧播报记录")
            
            return deleted_count
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"清理旧播报记录失败: {e}")
            return 0


# 全局播报管理器实例
_broadcast_manager = None

def get_broadcast_manager(db: Session) -> VoiceBroadcastManager:
    """获取播报管理器实例"""
    global _broadcast_manager
    if _broadcast_manager is None:
        _broadcast_manager = VoiceBroadcastManager(db)
    else:
        _broadcast_manager.db = db  # 更新数据库会话
    return _broadcast_manager
