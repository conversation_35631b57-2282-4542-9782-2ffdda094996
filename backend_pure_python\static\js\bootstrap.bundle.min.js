/* Bootstrap 5.3.0 简化版本 - 包含系统所需的基本JavaScript功能 */

(function() {
    'use strict';

    // Modal functionality
    class Modal {
        constructor(element) {
            this.element = element;
            this.backdrop = null;
        }

        show() {
            this.element.style.display = 'block';
            this.element.classList.add('show');
            this.showBackdrop();
            document.body.classList.add('modal-open');
        }

        hide() {
            this.element.style.display = 'none';
            this.element.classList.remove('show');
            this.hideBackdrop();
            document.body.classList.remove('modal-open');
        }

        showBackdrop() {
            if (!this.backdrop) {
                this.backdrop = document.createElement('div');
                this.backdrop.className = 'modal-backdrop fade show';
                this.backdrop.style.cssText = 'position:fixed;top:0;left:0;z-index:1050;width:100vw;height:100vh;background-color:#000;opacity:0.5';
                document.body.appendChild(this.backdrop);
                
                this.backdrop.addEventListener('click', () => {
                    this.hide();
                });
            }
        }

        hideBackdrop() {
            if (this.backdrop) {
                this.backdrop.remove();
                this.backdrop = null;
            }
        }

        static getInstance(element) {
            return element._modal || new Modal(element);
        }
    }

    // Dropdown functionality
    class Dropdown {
        constructor(element) {
            this.element = element;
            this.menu = element.nextElementSibling;
            this.isOpen = false;
        }

        toggle() {
            if (this.isOpen) {
                this.hide();
            } else {
                this.show();
            }
        }

        show() {
            this.menu.style.display = 'block';
            this.isOpen = true;
        }

        hide() {
            this.menu.style.display = 'none';
            this.isOpen = false;
        }

        static getInstance(element) {
            return element._dropdown || new Dropdown(element);
        }
    }

    // Alert functionality
    class Alert {
        constructor(element) {
            this.element = element;
        }

        close() {
            this.element.style.display = 'none';
        }

        static getInstance(element) {
            return element._alert || new Alert(element);
        }
    }

    // Initialize Bootstrap components
    document.addEventListener('DOMContentLoaded', function() {
        // Modal triggers
        document.querySelectorAll('[data-bs-toggle="modal"]').forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('data-bs-target'));
                if (target) {
                    const modal = Modal.getInstance(target);
                    target._modal = modal;
                    modal.show();
                }
            });
        });

        // Modal close buttons
        document.querySelectorAll('[data-bs-dismiss="modal"]').forEach(closeBtn => {
            closeBtn.addEventListener('click', function() {
                const modal = this.closest('.modal');
                if (modal && modal._modal) {
                    modal._modal.hide();
                }
            });
        });

        // Dropdown triggers
        document.querySelectorAll('[data-bs-toggle="dropdown"]').forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                e.preventDefault();
                const dropdown = Dropdown.getInstance(this);
                this._dropdown = dropdown;
                dropdown.toggle();
            });
        });

        // Alert close buttons
        document.querySelectorAll('[data-bs-dismiss="alert"]').forEach(closeBtn => {
            closeBtn.addEventListener('click', function() {
                const alert = this.closest('.alert');
                if (alert) {
                    const alertInstance = Alert.getInstance(alert);
                    alert._alert = alertInstance;
                    alertInstance.close();
                }
            });
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('[data-bs-toggle="dropdown"]')) {
                document.querySelectorAll('[data-bs-toggle="dropdown"]').forEach(trigger => {
                    if (trigger._dropdown && trigger._dropdown.isOpen) {
                        trigger._dropdown.hide();
                    }
                });
            }
        });
    });

    // Export to global scope
    window.bootstrap = {
        Modal: Modal,
        Dropdown: Dropdown,
        Alert: Alert
    };

})();
