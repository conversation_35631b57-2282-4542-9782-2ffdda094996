#!/usr/bin/env python3
"""
系统测试脚本
测试后端API的基本功能
"""

import requests
import json
import time
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

class SystemTester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.user_info = None
    
    def test_health(self) -> bool:
        """测试健康检查"""
        try:
            response = self.session.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                print("✅ 健康检查通过")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    def test_login(self, username: str = "admin", password: str = "admin123") -> bool:
        """测试用户登录"""
        try:
            data = {
                "username": username,
                "password": password
            }
            response = self.session.post(f"{BASE_URL}/api/auth/login", json=data)
            
            if response.status_code == 200:
                result = response.json()
                self.token = result["access_token"]
                self.user_info = result["user"]
                
                # 设置认证头
                self.session.headers.update({
                    "Authorization": f"Bearer {self.token}"
                })
                
                print(f"✅ 登录成功: {self.user_info['full_name']} ({self.user_info['role']})")
                return True
            else:
                print(f"❌ 登录失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def test_get_current_user(self) -> bool:
        """测试获取当前用户信息"""
        try:
            response = self.session.get(f"{BASE_URL}/api/auth/me")
            
            if response.status_code == 200:
                user = response.json()
                print(f"✅ 获取用户信息成功: {user['full_name']}")
                return True
            else:
                print(f"❌ 获取用户信息失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取用户信息异常: {e}")
            return False
    
    def test_get_tables(self) -> bool:
        """测试获取餐桌列表"""
        try:
            response = self.session.get(f"{BASE_URL}/api/tables")
            
            if response.status_code == 200:
                tables = response.json()
                print(f"✅ 获取餐桌列表成功: 共 {len(tables)} 个餐桌")
                return True
            else:
                print(f"❌ 获取餐桌列表失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取餐桌列表异常: {e}")
            return False
    
    def test_get_dishes(self) -> bool:
        """测试获取菜品列表"""
        try:
            response = self.session.get(f"{BASE_URL}/api/menu")
            
            if response.status_code == 200:
                result = response.json()
                dishes = result.get("dishes", [])
                print(f"✅ 获取菜品列表成功: 共 {len(dishes)} 个菜品")
                return True
            else:
                print(f"❌ 获取菜品列表失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取菜品列表异常: {e}")
            return False
    
    def test_get_orders(self) -> bool:
        """测试获取订单列表"""
        try:
            response = self.session.get(f"{BASE_URL}/api/orders")
            
            if response.status_code == 200:
                orders = response.json()
                print(f"✅ 获取订单列表成功: 共 {len(orders)} 个订单")
                return True
            else:
                print(f"❌ 获取订单列表失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取订单列表异常: {e}")
            return False
    
    def test_create_order(self) -> bool:
        """测试创建订单"""
        try:
            # 先获取一个餐桌和一些菜品
            tables_response = self.session.get(f"{BASE_URL}/api/tables")
            dishes_response = self.session.get(f"{BASE_URL}/api/menu")
            
            if tables_response.status_code != 200 or dishes_response.status_code != 200:
                print("❌ 无法获取餐桌或菜品信息")
                return False
            
            tables = tables_response.json()
            dishes_result = dishes_response.json()
            dishes = dishes_result.get("dishes", [])
            
            if not tables or not dishes:
                print("❌ 没有可用的餐桌或菜品")
                return False
            
            # 创建测试订单
            order_data = {
                "table_id": tables[0]["id"],
                "customer_name": "测试客户",
                "guest_count": 2,
                "items": [
                    {
                        "dish_id": dishes[0]["id"],
                        "quantity": 1
                    }
                ]
            }
            
            response = self.session.post(f"{BASE_URL}/api/orders", json=order_data)
            
            if response.status_code == 200:
                order = response.json()
                print(f"✅ 创建订单成功: {order['order_number']}")
                return True
            else:
                print(f"❌ 创建订单失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ 创建订单异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 50)
        print("暨阳湖大酒店传菜管理系统 - 系统测试")
        print("=" * 50)
        
        tests = [
            ("健康检查", self.test_health),
            ("用户登录", self.test_login),
            ("获取用户信息", self.test_get_current_user),
            ("获取餐桌列表", self.test_get_tables),
            ("获取菜品列表", self.test_get_dishes),
            ("获取订单列表", self.test_get_orders),
            ("创建订单", self.test_create_order),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🧪 测试: {test_name}")
            try:
                if test_func():
                    passed += 1
                time.sleep(0.5)  # 避免请求过快
            except Exception as e:
                print(f"❌ 测试异常: {e}")
        
        print("\n" + "=" * 50)
        print(f"测试完成: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！系统运行正常")
        else:
            print("⚠️  部分测试失败，请检查系统状态")
        
        print("=" * 50)
        
        return passed == total


def main():
    """主函数"""
    tester = SystemTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ 系统测试完成，可以开始使用系统")
        print("📱 前端地址: http://localhost:3000")
        print("🔧 后端地址: http://localhost:8000")
        print("📚 API 文档: http://localhost:8000/docs")
    else:
        print("\n❌ 系统测试失败，请检查服务状态")
        print("💡 确保后端服务已启动且数据库已初始化")


if __name__ == "__main__":
    main()
