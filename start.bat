@echo off
chcp 65001 >nul
title 暨阳湖大酒店传菜管理系统

echo ==========================================
echo 暨阳湖大酒店传菜管理系统
echo ==========================================

REM 检查 Python 版本
echo 检查 Python 环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装，请先安装 Python 3.9+
    pause
    exit /b 1
)

REM 检查 Node.js 版本
echo 检查 Node.js 环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装，请先安装 Node.js 18+
    pause
    exit /b 1
)

echo.
echo 选择启动模式：
echo 1. 完整启动 (后端 + 前端)
echo 2. 仅启动后端
echo 3. 仅启动前端
echo 4. 初始化数据库
echo 5. 安装依赖
set /p choice=请选择 (1-5): 

if "%choice%"=="1" goto full_start
if "%choice%"=="2" goto backend_only
if "%choice%"=="3" goto frontend_only
if "%choice%"=="4" goto init_db
if "%choice%"=="5" goto install_deps
echo 无效选择
pause
exit /b 1

:full_start
echo 启动完整系统...

REM 启动后端
echo 启动后端服务...
cd backend
python -m venv venv 2>nul
call venv\Scripts\activate.bat
pip install -r requirements.txt
python init_db.py
start "后端服务" cmd /k "uvicorn main:socket_app --reload --host 0.0.0.0 --port 8000"
cd ..

REM 等待后端启动
timeout /t 5 /nobreak >nul

REM 启动前端
echo 启动前端服务...
cd frontend
call npm install
start "前端服务" cmd /k "npm run dev"
cd ..

echo.
echo ✅ 系统启动成功！
echo 📱 前端地址: http://localhost:3000
echo 🔧 后端地址: http://localhost:8000
echo 📚 API 文档: http://localhost:8000/docs
echo.
echo 按任意键退出...
pause >nul
goto :eof

:backend_only
echo 启动后端服务...
cd backend
python -m venv venv 2>nul
call venv\Scripts\activate.bat
pip install -r requirements.txt
python init_db.py
uvicorn main:socket_app --reload --host 0.0.0.0 --port 8000
goto :eof

:frontend_only
echo 启动前端服务...
cd frontend
call npm install
npm run dev
goto :eof

:init_db
echo 初始化数据库...
cd backend
python -m venv venv 2>nul
call venv\Scripts\activate.bat
pip install -r requirements.txt
python init_db.py
echo ✅ 数据库初始化完成
pause
goto :eof

:install_deps
echo 安装后端依赖...
cd backend
python -m venv venv 2>nul
call venv\Scripts\activate.bat
pip install -r requirements.txt
cd ..

echo 安装前端依赖...
cd frontend
call npm install
cd ..

echo ✅ 依赖安装完成
pause
goto :eof
