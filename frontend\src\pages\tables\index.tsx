import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Table,
  Tag,
  Space,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  message,
  Popconfirm,
  Tooltip,
  Badge,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import MainLayout from '@/components/Layout/MainLayout';
import { useAuthStore } from '@/store';
import { tableApi } from '@/api';
import { Table as TableType, TableFormData, TableType as TableTypeEnum, TableStatus } from '@/types';
import {
  TABLE_TYPE_LABELS,
  TABLE_STATUS_LABELS,
  TABLE_STATUS_COLORS,
} from '@/utils/constants';
import { formatCurrency, formatDateTime } from '@/utils/helpers';

const { Option } = Select;

const TablesPage: React.FC = () => {
  const { user } = useAuthStore();
  const [tables, setTables] = useState<TableType[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTable, setEditingTable] = useState<TableType | null>(null);
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [selectedTable, setSelectedTable] = useState<TableType | null>(null);
  const [form] = Form.useForm();
  const [statusForm] = Form.useForm();

  // 加载餐桌列表
  const loadTables = async () => {
    try {
      setLoading(true);
      const data = await tableApi.getTables();
      setTables(data);
    } catch (error) {
      message.error('加载餐桌列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTables();
  }, []);

  // 处理创建/编辑餐桌
  const handleSubmit = async (values: TableFormData) => {
    try {
      if (editingTable) {
        await tableApi.updateTable(editingTable.id, values);
        message.success('餐桌信息更新成功');
      } else {
        await tableApi.createTable(values);
        message.success('餐桌创建成功');
      }
      setModalVisible(false);
      setEditingTable(null);
      form.resetFields();
      loadTables();
    } catch (error) {
      message.error(editingTable ? '更新餐桌失败' : '创建餐桌失败');
    }
  };

  // 处理删除餐桌
  const handleDelete = async (id: number) => {
    try {
      await tableApi.deleteTable(id);
      message.success('餐桌删除成功');
      loadTables();
    } catch (error) {
      message.error('删除餐桌失败');
    }
  };

  // 处理状态更新
  const handleStatusUpdate = async (values: any) => {
    if (!selectedTable) return;
    
    try {
      await tableApi.updateTableStatus(selectedTable.id, values);
      message.success('餐桌状态更新成功');
      setStatusModalVisible(false);
      setSelectedTable(null);
      statusForm.resetFields();
      loadTables();
    } catch (error) {
      message.error('更新餐桌状态失败');
    }
  };

  // 打开编辑模态框
  const openEditModal = (table?: TableType) => {
    setEditingTable(table || null);
    setModalVisible(true);
    
    if (table) {
      form.setFieldsValue({
        ...table,
        table_type: table.table_type,
      });
    } else {
      form.resetFields();
    }
  };

  // 打开状态更新模态框
  const openStatusModal = (table: TableType) => {
    setSelectedTable(table);
    setStatusModalVisible(true);
    statusForm.setFieldsValue({
      status: table.status,
      current_guests: table.current_guests,
      estimated_duration: table.estimated_duration,
      assigned_waiter_id: table.assigned_waiter_id,
    });
  };

  // 表格列定义
  const columns: ColumnsType<TableType> = [
    {
      title: '桌号',
      dataIndex: 'number',
      key: 'number',
      width: 100,
      render: (text, record) => (
        <Space>
          <strong>{text}</strong>
          {record.table_type === TableTypeEnum.VIP_ROOM && (
            <Tag color="gold">VIP</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '类型',
      dataIndex: 'table_type',
      key: 'table_type',
      width: 100,
      render: (type) => TABLE_TYPE_LABELS[type],
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={TABLE_STATUS_COLORS[status]}>
          {TABLE_STATUS_LABELS[status]}
        </Tag>
      ),
    },
    {
      title: '容量',
      dataIndex: 'capacity',
      key: 'capacity',
      width: 80,
      render: (capacity, record) => (
        <span>
          {record.current_guests > 0 && (
            <Badge count={record.current_guests} size="small" />
          )}
          {capacity}人
        </span>
      ),
    },
    {
      title: '位置',
      key: 'location',
      width: 120,
      render: (_, record) => (
        <span>
          {record.floor && `${record.floor} `}
          {record.area}
        </span>
      ),
    },
    {
      title: '设施',
      dataIndex: 'facilities_list',
      key: 'facilities',
      width: 150,
      render: (facilities: string[]) => (
        <Space wrap>
          {facilities.slice(0, 3).map((facility) => (
            <Tag key={facility} size="small">
              {facility}
            </Tag>
          ))}
          {facilities.length > 3 && (
            <Tooltip title={facilities.slice(3).join(', ')}>
              <Tag size="small">+{facilities.length - 3}</Tag>
            </Tooltip>
          )}
        </Space>
      ),
    },
    {
      title: '最低消费',
      dataIndex: 'minimum_charge',
      key: 'minimum_charge',
      width: 100,
      render: (amount) => amount ? formatCurrency(amount) : '-',
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => openEditModal(record)}
            />
          </Tooltip>
          
          <Tooltip title="更新状态">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => openStatusModal(record)}
            />
          </Tooltip>
          
          {user?.has_permission?.('table.manage') && (
            <>
              <Tooltip title="编辑">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => openEditModal(record)}
                />
              </Tooltip>
              
              <Popconfirm
                title="确定要删除这个餐桌吗？"
                onConfirm={() => handleDelete(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Tooltip title="删除">
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                  />
                </Tooltip>
              </Popconfirm>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* 页面头部 */}
        <Card>
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold mb-2">餐桌管理</h2>
              <p className="text-gray-600">管理酒店的餐桌和包厢信息</p>
            </div>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadTables}
                loading={loading}
              >
                刷新
              </Button>
              
              {user?.has_permission?.('table.manage') && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => openEditModal()}
                >
                  新增餐桌
                </Button>
              )}
            </Space>
          </div>
        </Card>

        {/* 餐桌列表 */}
        <Card>
          <Table
            columns={columns}
            dataSource={tables}
            rowKey="id"
            loading={loading}
            scroll={{ x: 1200 }}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 个餐桌`,
            }}
          />
        </Card>
      </div>

      {/* 创建/编辑餐桌模态框 */}
      <Modal
        title={editingTable ? '编辑餐桌' : '新增餐桌'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingTable(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            table_type: TableTypeEnum.HALL_TABLE,
            capacity: 4,
            has_wifi: true,
            has_air_conditioning: true,
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="number"
                label="桌号"
                rules={[{ required: true, message: '请输入桌号' }]}
              >
                <Input placeholder="如：A01" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="name" label="名称">
                <Input placeholder="如：大厅1号桌" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="table_type"
                label="类型"
                rules={[{ required: true, message: '请选择餐桌类型' }]}
              >
                <Select>
                  {Object.entries(TABLE_TYPE_LABELS).map(([value, label]) => (
                    <Option key={value} value={value}>
                      {label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="capacity"
                label="容纳人数"
                rules={[{ required: true, message: '请输入容纳人数' }]}
              >
                <InputNumber min={1} max={50} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="floor" label="楼层">
                <Input placeholder="如：1F" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="area" label="区域">
                <Input placeholder="如：大厅A区" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="location_description" label="位置描述">
            <Input.TextArea rows={2} placeholder="详细位置描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="minimum_charge" label="最低消费">
                <InputNumber
                  style={{ width: '100%' }}
                  formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => value!.replace(/¥\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="service_charge_rate" label="服务费率(%)">
                <InputNumber min={0} max={100} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="hourly_rate" label="包厢时费">
                <InputNumber
                  style={{ width: '100%' }}
                  formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => value!.replace(/¥\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
          </Row>

          <div className="mb-4">
            <h4 className="mb-3">设施配置</h4>
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item name="has_tv" valuePropName="checked">
                  <Switch checkedChildren="电视" unCheckedChildren="电视" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="has_karaoke" valuePropName="checked">
                  <Switch checkedChildren="KTV" unCheckedChildren="KTV" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="has_mahjong" valuePropName="checked">
                  <Switch checkedChildren="麻将" unCheckedChildren="麻将" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="has_projector" valuePropName="checked">
                  <Switch checkedChildren="投影仪" unCheckedChildren="投影仪" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item name="has_wifi" valuePropName="checked">
                  <Switch checkedChildren="WiFi" unCheckedChildren="WiFi" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="has_air_conditioning" valuePropName="checked">
                  <Switch checkedChildren="空调" unCheckedChildren="空调" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="is_vip_only" valuePropName="checked">
                  <Switch checkedChildren="VIP专用" unCheckedChildren="VIP专用" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="requires_reservation" valuePropName="checked">
                  <Switch checkedChildren="需预订" unCheckedChildren="需预订" />
                </Form.Item>
              </Col>
            </Row>
          </div>

          <Form.Item name="notes" label="备注">
            <Input.TextArea rows={3} placeholder="其他备注信息" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingTable ? '更新' : '创建'}
              </Button>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingTable(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 状态更新模态框 */}
      <Modal
        title="更新餐桌状态"
        open={statusModalVisible}
        onCancel={() => {
          setStatusModalVisible(false);
          setSelectedTable(null);
          statusForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={statusForm}
          layout="vertical"
          onFinish={handleStatusUpdate}
        >
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select>
              {Object.entries(TABLE_STATUS_LABELS).map(([value, label]) => (
                <Option key={value} value={value}>
                  {label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="current_guests" label="当前客人数">
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name="estimated_duration" label="预计用餐时长(分钟)">
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name="assigned_waiter_id" label="分配服务员">
            <Select allowClear placeholder="选择服务员">
              {/* 这里应该从API获取服务员列表 */}
            </Select>
          </Form.Item>

          <Form.Item name="special_requirements" label="特殊要求">
            <Input.TextArea rows={3} />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                更新状态
              </Button>
              <Button
                onClick={() => {
                  setStatusModalVisible(false);
                  setSelectedTable(null);
                  statusForm.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </MainLayout>
  );
};

export default TablesPage;
