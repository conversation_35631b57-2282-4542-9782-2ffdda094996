version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15
    container_name: paocai_postgres
    environment:
      POSTGRES_DB: paocai_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - paocai_network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: paocai_redis
    ports:
      - "6379:6379"
    networks:
      - paocai_network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: paocai_backend
    environment:
      DATABASE_URL: ********************************************/paocai_db
      REDIS_URL: redis://redis:6379/0
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - backend_static:/app/static
    networks:
      - paocai_network
    restart: unless-stopped

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: paocai_frontend
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8000
      NEXT_PUBLIC_WS_URL: ws://localhost:8000
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - paocai_network
    restart: unless-stopped

volumes:
  postgres_data:
  backend_static:

networks:
  paocai_network:
    driver: bridge
