import { api } from './client';
import { LoginRequest, LoginResponse, User } from '@/types';

export const authApi = {
  // 用户登录
  login: (data: LoginRequest): Promise<LoginResponse> => {
    return api.post('/api/auth/login', data);
  },
  
  // 用户登出
  logout: (): Promise<{ message: string }> => {
    return api.post('/api/auth/logout');
  },
  
  // 获取当前用户信息
  getCurrentUser: (): Promise<User> => {
    return api.get('/api/auth/me');
  },
  
  // 刷新令牌
  refreshToken: (): Promise<{ access_token: string; token_type: string }> => {
    return api.post('/api/auth/refresh');
  },
  
  // 修改密码
  changePassword: (oldPassword: string, newPassword: string): Promise<{ message: string }> => {
    return api.post('/api/auth/change-password', {
      old_password: oldPassword,
      new_password: newPassword,
    });
  },
};
