{% extends "base.html" %}

{% block title %}指令管理 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-chat-square-text"></i>
        指令管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCommandModal">
            <i class="bi bi-plus-circle"></i>
            添加指令
        </button>
    </div>
</div>

<!-- 指令说明 -->
<div class="alert alert-info">
    <h6><i class="bi bi-info-circle"></i> 指令管理说明</h6>
    <p class="mb-0">
        这里配置服务员可以发送的指令集。每个指令都会在厨房端进行语音播报，帮助厨房打荷及时响应服务员的需求。
    </p>
</div>

<!-- 指令列表 -->
<div class="card shadow">
    <div class="card-header">
        <h5 class="mb-0">指令列表</h5>
        <small class="text-muted">配置服务员可以发送的指令模板</small>
    </div>
    <div class="card-body">
        {% if commands %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th width="12%">指令名称</th>
                        <th width="8%">指令代码</th>
                        <th width="8%">分类</th>
                        <th width="20%">描述</th>
                        <th width="15%">语音播报</th>
                        <th width="10%">手动输入</th>
                        <th width="8%">状态</th>
                        <th width="19%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for command in commands %}
                    <tr>
                        <td>
                            <strong>{{ command.name }}</strong>
                        </td>
                        <td>
                            <code>{{ command.code }}</code>
                        </td>
                        <td>
                            {% if command.category == 'service' %}
                            <span class="badge bg-primary">服务类</span>
                            {% elif command.category == 'food' %}
                            <span class="badge bg-success">菜品类</span>
                            {% elif command.category == 'urgent' %}
                            <span class="badge bg-warning">紧急类</span>
                            {% else %}
                            <span class="badge bg-secondary">通用类</span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ command.description or '-' }}</small>
                        </td>
                        <td>
                            <small class="text-primary">
                                <i class="bi bi-volume-up"></i>
                                {{ command.voice_text or command.name }}
                            </small>
                        </td>
                        <td>
                            {% if command.allow_input %}
                            <span class="badge bg-success">
                                <i class="bi bi-check-circle"></i> ✅ 允许
                            </span>
                            {% if command.input_placeholder %}
                            <br>
                            <small class="text-muted">{{ command.input_placeholder }}</small>
                            {% endif %}
                            {% else %}
                            <span class="badge bg-danger">
                                <i class="bi bi-x-circle"></i> ❌ 不允许
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if command.is_active %}
                            <span class="badge bg-success">启用</span>
                            {% else %}
                            <span class="badge bg-secondary">禁用</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" 
                                        onclick="editCommand({{ command.id }})" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" 
                                        onclick="testVoice('{{ command.voice_text or command.name }}')" title="测试语音">
                                    <i class="bi bi-play-circle"></i>
                                </button>
                                {% if command.is_active %}
                                <button type="button" class="btn btn-outline-warning" 
                                        onclick="toggleCommand({{ command.id }}, false)" title="禁用">
                                    <i class="bi bi-pause-circle"></i>
                                </button>
                                {% else %}
                                <button type="button" class="btn btn-outline-success" 
                                        onclick="toggleCommand({{ command.id }}, true)" title="启用">
                                    <i class="bi bi-play-circle"></i>
                                </button>
                                {% endif %}
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="deleteCommand({{ command.id }}, '{{ command.name }}')" title="删除">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-chat-square-text text-muted" style="font-size: 4rem;"></i>
            <h4 class="text-muted mt-3">暂无指令</h4>
            <p class="text-muted">点击"添加指令"按钮创建第一个指令模板</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 添加指令模态框 -->
<div class="modal fade" id="addCommandModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加指令</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addCommandForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="commandName" class="form-label">指令名称 *</label>
                        <input type="text" class="form-control" id="commandName" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="commandCode" class="form-label">指令代码 *</label>
                        <input type="text" class="form-control" id="commandCode" name="code" required readonly>
                        <div class="form-text">系统自动生成，基于指令名称</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="commandCategory" class="form-label">分类</label>
                        <select class="form-select" id="commandCategory" name="category">
                            <option value="general">通用类</option>
                            <option value="service">服务类</option>
                            <option value="food">菜品类</option>
                            <option value="urgent">紧急类</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="commandDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="commandDescription" name="description" rows="2"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="commandVoiceText" class="form-label">语音播报文本</label>
                        <input type="text" class="form-control" id="commandVoiceText" name="voice_text">
                        <div class="form-text">厨房端播报的语音内容，留空则使用指令名称</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="allowInput" name="allow_input">
                            <label class="form-check-label" for="allowInput">
                                允许服务员手动输入内容
                            </label>
                        </div>
                        <div class="form-text">勾选后，服务员发送此指令时可以输入自定义内容</div>
                    </div>

                    <div class="mb-3" id="inputSettings" style="display: none;">
                        <label for="inputPlaceholder" class="form-label">输入提示文字</label>
                        <input type="text" class="form-control" id="inputPlaceholder" name="input_placeholder"
                               placeholder="例如：请输入酒水饮料名称">

                        <div class="form-check mt-2">
                            <input class="form-check-input" type="checkbox" id="inputRequired" name="input_required">
                            <label class="form-check-label" for="inputRequired">
                                必须输入内容
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">添加</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 编辑指令模态框 -->
<div class="modal fade" id="editTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑指令模板</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editTemplateForm">
                <div class="modal-body">
                    <input type="hidden" id="editTemplateId" name="template_id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editName" class="form-label">指令名称 *</label>
                                <input type="text" class="form-control" id="editName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editCode" class="form-label">指令代码 *</label>
                                <input type="text" class="form-control" id="editCode" name="code" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editCategory" class="form-label">分类 *</label>
                        <select class="form-select" id="editCategory" name="category" required>
                            <option value="">请选择分类</option>
                            <option value="general">通用指令</option>
                            <option value="service">服务指令</option>
                            <option value="kitchen">厨房指令</option>
                            <option value="special">特殊指令</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="editDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="editDescription" name="description" rows="2"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="editVoiceText" class="form-label">语音播报文本</label>
                        <input type="text" class="form-control" id="editVoiceText" name="voice_text">
                        <div class="form-text">留空则使用指令名称作为语音播报内容</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editAllowInput" name="allow_input" onchange="toggleEditInputConfig()">
                            <label class="form-check-label" for="editAllowInput">
                                允许手工输入
                            </label>
                        </div>
                    </div>

                    <div id="editInputConfig" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">输入配置</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="editInputPlaceholder" class="form-label">输入提示文本</label>
                                    <input type="text" class="form-control" id="editInputPlaceholder" name="input_placeholder">
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="editInputRequired" name="input_required">
                                        <label class="form-check-label" for="editInputRequired">
                                            必须输入内容
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存修改</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // 自动生成指令代码
    document.getElementById('commandName').addEventListener('input', function(e) {
        const name = e.target.value;
        const code = generateCommandCode(name);
        document.getElementById('commandCode').value = code;
    });

    // 控制输入设置显示
    document.getElementById('allowInput').addEventListener('change', function(e) {
        const inputSettings = document.getElementById('inputSettings');
        if (e.target.checked) {
            inputSettings.style.display = 'block';
        } else {
            inputSettings.style.display = 'none';
        }
    });

    // 生成指令代码
    function generateCommandCode(name) {
        if (!name) return '';

        // 中文转拼音映射
        const pinyinMap = {
            '上菜': 'serve_dish',
            '催菜': 'rush_dish',
            '收脏餐': 'clean_table',
            '上主食': 'add_staple',
            '加酒水': 'add_drink',
            '加饮料': 'add_beverage',
            '澳龙泡饭': 'aolong_rice',
            '上水果': 'serve_fruit',
            '换菜': 'change_dish',
            '特殊服务': 'special_service',
            '结账': 'checkout',
            '打包': 'takeaway'
        };

        // 如果有直接映射，使用映射
        if (pinyinMap[name]) {
            return pinyinMap[name];
        }

        // 否则生成简单代码
        return name.toLowerCase().replace(/[^a-z0-9\u4e00-\u9fa5]/g, '_').replace(/_{2,}/g, '_').replace(/^_|_$/g, '');
    }

    // 添加指令
    document.getElementById('addCommandForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        // 处理复选框值
        data.allow_input = formData.has('allow_input');
        data.input_required = formData.has('input_required');

        console.log('提交指令数据:', data);

        fetch('/command-templates', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        }).then(response => {
            if (response.ok) {
                alert('指令添加成功！');
                location.reload();
            } else {
                response.json().then(err => {
                    alert('添加失败: ' + (err.message || '未知错误'));
                });
            }
        }).catch(error => {
            console.error('添加指令失败:', error);
            alert('添加失败，请重试');
        });
    });
    
    // 切换指令状态
    function toggleCommand(id, isActive) {
        fetch(`/command-templates/${id}/toggle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({is_active: isActive})
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('操作失败');
            }
        });
    }
    
    // 删除指令
    function deleteCommand(id, name) {
        if (confirm(`确定要删除指令"${name}"吗？`)) {
            fetch(`/command-templates/${id}`, {
                method: 'DELETE'
            }).then(response => {
                if (response.ok) {
                    location.reload();
                } else {
                    alert('删除失败');
                }
            });
        }
    }
    
    // 测试语音
    function testVoice(text) {
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'zh-CN';
            speechSynthesis.speak(utterance);
        } else {
            alert('您的浏览器不支持语音播报功能');
        }
    }
    
    // 编辑指令
    function editCommand(id) {
        // 获取指令信息
        fetch('/command-templates/' + id)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const template = data.template;

                    // 填充编辑表单
                    document.getElementById('editTemplateId').value = template.id;
                    document.getElementById('editName').value = template.name;
                    document.getElementById('editCode').value = template.code;
                    document.getElementById('editCategory').value = template.category;
                    document.getElementById('editDescription').value = template.description || '';
                    document.getElementById('editVoiceText').value = template.voice_text || '';
                    document.getElementById('editAllowInput').checked = template.allow_input || false;
                    document.getElementById('editInputPlaceholder').value = template.input_placeholder || '';
                    document.getElementById('editInputRequired').checked = template.input_required || false;

                    // 显示/隐藏输入配置
                    toggleEditInputConfig();

                    // 显示编辑模态框
                    new bootstrap.Modal(document.getElementById('editTemplateModal')).show();
                } else {
                    alert('获取指令信息失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('获取指令信息失败');
            });
    }

    // 切换编辑表单的输入配置显示
    function toggleEditInputConfig() {
        const allowInput = document.getElementById('editAllowInput').checked;
        const inputConfig = document.getElementById('editInputConfig');
        inputConfig.style.display = allowInput ? 'block' : 'none';
    }

    // 处理编辑表单提交
    document.getElementById('editTemplateForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const templateId = formData.get('template_id');

        const updateData = {
            name: formData.get('name'),
            code: formData.get('code'),
            category: formData.get('category'),
            description: formData.get('description'),
            voice_text: formData.get('voice_text'),
            allow_input: formData.has('allow_input'),
            input_placeholder: formData.get('input_placeholder'),
            input_required: formData.has('input_required')
        };

        fetch('/command-templates/' + templateId + '/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(updateData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('指令更新成功');
                bootstrap.Modal.getInstance(document.getElementById('editTemplateModal')).hide();
                location.reload();
            } else {
                alert('更新失败: ' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('更新失败');
        });
    });
</script>
{% endblock %}
