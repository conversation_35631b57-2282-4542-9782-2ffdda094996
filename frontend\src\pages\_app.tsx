import React from 'react';
import type { AppProps } from 'next/app';
import Head from 'next/head';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import relativeTime from 'dayjs/plugin/relativeTime';
import '@/styles/globals.css';

// 配置 dayjs
dayjs.locale('zh-cn');
dayjs.extend(relativeTime);

// Ant Design 主题配置
const theme = {
  token: {
    colorPrimary: '#f2750a', // 暨阳湖主题色
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#f5222d',
    colorInfo: '#1890ff',
    borderRadius: 6,
    fontFamily: 'PingFang SC, Microsoft YaHei, SimHei, sans-serif',
  },
  components: {
    Layout: {
      headerBg: '#ffffff',
      siderBg: '#001529',
    },
    Menu: {
      darkItemBg: '#001529',
      darkSubMenuItemBg: '#000c17',
    },
    Button: {
      primaryShadow: '0 2px 0 rgba(242, 117, 10, 0.1)',
    },
  },
};

function MyApp({ Component, pageProps }: AppProps) {
  return (
    <>
      <Head>
        <title>暨阳湖大酒店传菜管理系统</title>
        <meta name="description" content="现代化的餐厅管理系统，提供完整的服务流程管理" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
        
        {/* 预加载字体 */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* PWA 相关 */}
        <meta name="theme-color" content="#f2750a" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="暨阳湖大酒店" />
        
        {/* 防止缩放 */}
        <meta 
          name="viewport" 
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" 
        />
      </Head>
      
      <ConfigProvider
        locale={zhCN}
        theme={theme}
        componentSize="middle"
      >
        <Component {...pageProps} />
      </ConfigProvider>
    </>
  );
}

export default MyApp;
