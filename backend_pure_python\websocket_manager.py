"""
简单的WebSocket管理器
用于在菜品状态更新时通知服务员页面刷新
"""

import asyncio
import json
from typing import Dict, Set
import logging

logger = logging.getLogger(__name__)

class SimpleWebSocketManager:
    """简单的WebSocket管理器"""
    
    def __init__(self):
        self.connections: Dict[str, Set] = {
            'waiters': set(),
            'kitchen': set(),
            'management': set()
        }
    
    async def broadcast_to_all(self, message: dict):
        """广播消息到所有连接（模拟实现）"""
        try:
            # 由于这是纯Python版本，我们使用日志记录来模拟WebSocket广播
            # 在实际部署中，这里应该连接到真正的WebSocket服务
            
            message_type = message.get('type', 'unknown')
            print(f"📡 WebSocket广播: {message_type}")
            
            if message_type == 'dish_status_updated':
                # 菜品状态更新，通知服务员刷新
                dish_id = message.get('dish_id')
                status = message.get('status')
                room_number = message.get('room_number')
                print(f"🔔 通知服务员: 包厢{room_number}菜品{dish_id}状态更新为{status}")
                
            elif message_type == 'guest_count_updated':
                # 人数更新
                room_number = message.get('room_number')
                guest_count = message.get('guest_count')
                print(f"👥 人数更新: 包厢{room_number}人数更新为{guest_count}")
                
            elif message_type == 'voice_config_updated':
                # 语音配置更新
                print(f"🔊 语音配置更新")

            elif message_type == 'dining_ended':
                # 结束用餐，触发大屏刷新
                room_number = message.get('room_number')
                waiter_name = message.get('waiter_name')
                print(f"🏁 结束用餐通知: 包厢{room_number}用餐结束，触发大屏刷新")

            elif message_type == 'kitchen_refresh':
                # 厨房大屏强制刷新
                print(f"🔄 厨房大屏强制刷新")

            elif message_type == 'waiter_instruction':
                # 服务员指令通知
                room_number = message.get('room_number')
                instruction = message.get('instruction')
                print(f"📢 服务员指令: 包厢{room_number} - {instruction}")

            elif message_type == 'dish_ready':
                # 菜品制作完成通知
                room_number = message.get('room_number')
                dish_name = message.get('dish_name')
                print(f"🍽️ 菜品完成广播: 包厢{room_number}的{dish_name}制作完成")

            return True
            
        except Exception as e:
            logger.error(f"WebSocket广播失败: {e}")
            return False
    
    async def broadcast_to_waiters(self, message: dict):
        """广播消息到所有服务员"""
        try:
            print(f"📱 通知服务员: {message}")
            return True
        except Exception as e:
            logger.error(f"通知服务员失败: {e}")
            return False
    
    async def broadcast_dish_ready(self, dish_id: int, room_number: str, dish_name: str):
        """广播菜品制作完成消息"""
        message = {
            'type': 'dish_ready',
            'dish_id': dish_id,
            'room_number': room_number,
            'dish_name': dish_name,
            'message': f'包厢{room_number}的{dish_name}已制作完成，请及时上菜',
            'timestamp': asyncio.get_event_loop().time()
        }

        print(f"🍽️ 菜品完成通知: 包厢{room_number}的{dish_name}已制作完成")

        # 广播到所有连接，包括厨房大屏
        await self.broadcast_to_all(message)

        # 同时通知服务员
        await self.broadcast_to_waiters(message)

        return True

    async def broadcast_dish_completion_from_helper(self, room_number: str, dish_name: str, message: str):
        """广播打荷员确认菜品完成的消息（只发送到厨房大屏）"""
        websocket_message = {
            'type': 'dish_completion_from_helper',
            'room_number': room_number,
            'dish_name': dish_name,
            'message': message,
            'timestamp': asyncio.get_event_loop().time()
        }

        print(f"🍽️ 打荷员菜品完成通知: {message}")

        # 只广播到厨房大屏，不发送给打荷员界面
        await self.broadcast_to_all(websocket_message)

        return True

    async def broadcast_dining_ended(self, room_number: str, waiter_name: str):
        """广播结束用餐消息，触发大屏刷新"""
        message = {
            'type': 'dining_ended',
            'room_number': room_number,
            'waiter_name': waiter_name,
            'timestamp': asyncio.get_event_loop().time()
        }

        print(f"🏁 结束用餐广播: 包厢{room_number}用餐结束，触发厨房大屏刷新")

        # 广播到所有连接，特别是厨房大屏
        await self.broadcast_to_all(message)

        # 额外发送厨房刷新指令
        await self.broadcast_kitchen_refresh()

        return True

    async def broadcast_kitchen_refresh(self):
        """广播厨房大屏刷新指令"""
        message = {
            'type': 'kitchen_refresh',
            'timestamp': asyncio.get_event_loop().time()
        }

        print(f"🔄 厨房大屏刷新广播")
        await self.broadcast_to_all(message)

        return True

    async def broadcast_waiter_instruction(self, room_number: str, instruction: str, waiter_name: str):
        """广播服务员指令到大屏"""
        message = {
            'type': 'waiter_instruction',
            'room_number': room_number,
            'instruction': instruction,
            'waiter_name': waiter_name,
            'timestamp': asyncio.get_event_loop().time()
        }

        print(f"📢 服务员指令广播: 包厢{room_number} - {instruction}")
        await self.broadcast_to_all(message)

        return True

# 全局WebSocket管理器实例
_websocket_manager = None

def get_websocket_manager() -> SimpleWebSocketManager:
    """获取WebSocket管理器实例"""
    global _websocket_manager
    if _websocket_manager is None:
        _websocket_manager = SimpleWebSocketManager()
    return _websocket_manager

# 为了兼容现有代码，创建一个别名
websocket_manager = get_websocket_manager()
