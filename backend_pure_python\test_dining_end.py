#!/usr/bin/env python3
"""
测试用餐结束状态重置机制
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8001"

def login(username, password):
    """登录获取token"""
    login_data = {
        "username": username,
        "password": password
    }
    
    response = requests.post(f"{BASE_URL}/login", data=login_data)
    if response.status_code == 200:
        # 检查响应是否包含token
        if "token" in response.text or "access_token" in response.text:
            try:
                return response.json()["access_token"]
            except:
                return None
        else:
            print("登录成功但未找到token")
            return None
    else:
        print(f"登录失败: {response.status_code}")
        return None

def test_waiter_finish_dining(token, room_number):
    """测试服务员结束用餐"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(
        f"{BASE_URL}/waiter/finish-room-dining/{room_number}",
        headers=headers
    )
    
    print(f"服务员结束用餐 - 状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    return response.status_code == 200

def test_manager_force_end(token, room_number):
    """测试餐饮经理强制结束"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "room_number": room_number,
        "reason": "测试强制结束"
    }
    
    response = requests.post(
        f"{BASE_URL}/tables/{room_number}/force-end",
        headers=headers,
        json=data
    )
    
    print(f"餐饮经理强制结束 - 状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    return response.status_code == 200

def test_admin_force_end(token, room_number):
    """测试系统管理员强制结束"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "room_number": room_number,
        "reason": "测试系统管理员强制结束"
    }
    
    response = requests.post(
        f"{BASE_URL}/admin/force-end-dining",
        headers=headers,
        json=data
    )
    
    print(f"系统管理员强制结束 - 状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    return response.status_code == 200

def check_room_status(room_number):
    """检查包厢状态"""
    response = requests.get(f"{BASE_URL}/api/tables")
    if response.status_code == 200:
        tables = response.json()
        for table in tables:
            if table["number"] == room_number:
                print(f"包厢 {room_number} 状态: {table['status']}")
                print(f"当前客人数: {table.get('current_guests', 0)}")
                print(f"分配服务员: {table.get('assigned_waiter_id', 'None')}")
                return table
    return None

def main():
    print("🧪 测试用餐结束状态重置机制")
    print("=" * 50)
    
    # 测试包厢
    test_room = "2号赏湖厅"
    
    print(f"📋 测试包厢: {test_room}")
    print()
    
    # 检查初始状态
    print("1. 检查包厢初始状态:")
    initial_status = check_room_status(test_room)
    print()
    
    # 测试服务员结束用餐
    print("2. 测试服务员结束用餐:")
    waiter_token = login("waiter01", "waiter123")
    if waiter_token:
        print("✅ 服务员登录成功")
        test_waiter_finish_dining(waiter_token, test_room)
    else:
        print("❌ 服务员登录失败")
    print()
    
    # 检查结束后状态
    print("3. 检查结束后包厢状态:")
    final_status = check_room_status(test_room)
    print()
    
    # 测试餐饮经理强制结束
    print("4. 测试餐饮经理强制结束:")
    manager_token = login("manager01", "manager123")
    if manager_token:
        print("✅ 餐饮经理登录成功")
        test_manager_force_end(manager_token, "8")  # 测试另一个包厢
    else:
        print("❌ 餐饮经理登录失败")
    print()
    
    # 测试系统管理员强制结束
    print("5. 测试系统管理员强制结束:")
    admin_token = login("admin", "admin123")
    if admin_token:
        print("✅ 系统管理员登录成功")
        test_admin_force_end(admin_token, "6")  # 测试另一个包厢
    else:
        print("❌ 系统管理员登录失败")
    print()
    
    print("🎯 测试完成!")

if __name__ == "__main__":
    main()
