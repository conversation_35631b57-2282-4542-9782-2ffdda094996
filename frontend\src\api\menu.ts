import { api } from './client';
import { Dish, DishFormData, DishCategory, DishStatus, SpicyLevel, CookingMethod, PaginatedResponse } from '@/types';

export const menuApi = {
  // 获取菜品列表
  getDishes: (params?: {
    page?: number;
    size?: number;
    search?: string;
    category?: DishCategory;
    status?: DishStatus;
    spicy_level?: SpicyLevel;
    cooking_method?: CookingMethod;
    is_recommended?: boolean;
    is_signature?: boolean;
    is_new?: boolean;
    available_only?: boolean;
  }): Promise<PaginatedResponse<Dish>> => {
    return api.get('/api/menu', { params });
  },
  
  // 创建菜品
  createDish: (data: DishFormData): Promise<Dish> => {
    return api.post('/api/menu', data);
  },
  
  // 获取菜品详情
  getDish: (id: number): Promise<Dish> => {
    return api.get(`/api/menu/${id}`);
  },
  
  // 更新菜品信息
  updateDish: (id: number, data: Partial<DishFormData>): Promise<Dish> => {
    return api.put(`/api/menu/${id}`, data);
  },
  
  // 删除菜品
  deleteDish: (id: number): Promise<{ message: string }> => {
    return api.delete(`/api/menu/${id}`);
  },
  
  // 上传菜品图片
  uploadDishImage: (id: number, file: File): Promise<{ message: string; image_url: string }> => {
    const formData = new FormData();
    formData.append('file', file);
    return api.upload(`/api/menu/${id}/upload-image`, formData);
  },
  
  // 获取菜品分类列表
  getDishCategories: (): Promise<{ categories: Array<{ value: string; label: string }> }> => {
    return api.get('/api/menu/categories/list');
  },
  
  // 获取热门菜品
  getPopularDishes: (limit?: number): Promise<Array<{
    id: number;
    name: string;
    sales_count: number;
    price: number;
    image_url?: string;
  }>> => {
    return api.get('/api/menu/stats/popular', { params: { limit } });
  },
};
