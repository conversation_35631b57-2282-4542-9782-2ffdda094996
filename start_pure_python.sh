#!/bin/bash

# 暨阳湖大酒店传菜管理系统 - 纯Python版本启动脚本

echo "=========================================="
echo "暨阳湖大酒店传菜管理系统 (纯Python版本)"
echo "=========================================="

# 检查 Python 版本
echo "检查 Python 环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python 3 未安装，请先安装 Python 3.9+"
    exit 1
fi

echo ""
echo "选择操作："
echo "1. 完整启动 (初始化数据库 + 启动服务)"
echo "2. 仅启动服务"
echo "3. 仅初始化数据库"
echo "4. 安装依赖"
echo "5. 重置数据库"
read -p "请选择 (1-5): " choice

case $choice in
    1)
        echo "完整启动系统..."
        
        # 进入纯Python版本目录
        cd backend_pure_python
        
        # 创建虚拟环境
        echo "创建Python虚拟环境..."
        python3 -m venv venv 2>/dev/null || true
        source venv/bin/activate
        
        # 升级pip
        echo "升级pip..."
        pip install --upgrade pip

        # 安装依赖
        echo "安装Python依赖..."
        pip install fastapi uvicorn sqlalchemy python-jose passlib python-multipart pydantic pydantic-settings jinja2 aiofiles bcrypt cryptography
        
        # 初始化数据库
        echo "初始化数据库..."
        python init_db.py
        
        # 启动服务
        echo "启动服务..."
        echo ""
        echo "✅ 系统启动成功！"
        echo "🌐 访问地址: http://localhost:8000"
        echo "📚 API 文档: http://localhost:8000/docs"
        echo ""
        echo "默认登录账号:"
        echo "管理员: admin / admin123"
        echo "经理: manager01 / manager123"
        echo "服务员: waiter01 / waiter123"
        echo "厨师长: chef01 / chef123"
        echo ""
        echo "按 Ctrl+C 停止服务"
        
        python main.py
        ;;
        
    2)
        echo "启动服务..."
        cd backend_pure_python
        python3 -m venv venv 2>/dev/null || true
        source venv/bin/activate
        pip install --upgrade pip
        pip install fastapi uvicorn sqlalchemy python-jose passlib python-multipart pydantic pydantic-settings jinja2 aiofiles bcrypt cryptography
        python main.py
        ;;

    3)
        echo "初始化数据库..."
        cd backend_pure_python
        python3 -m venv venv 2>/dev/null || true
        source venv/bin/activate
        pip install --upgrade pip
        pip install fastapi uvicorn sqlalchemy python-jose passlib python-multipart pydantic pydantic-settings jinja2 aiofiles bcrypt cryptography
        python init_db.py
        echo "✅ 数据库初始化完成"
        ;;

    4)
        echo "安装依赖..."
        cd backend_pure_python
        python3 -m venv venv 2>/dev/null || true
        source venv/bin/activate
        pip install --upgrade pip
        pip install fastapi uvicorn sqlalchemy python-jose passlib python-multipart pydantic pydantic-settings jinja2 aiofiles bcrypt cryptography
        echo "✅ 依赖安装完成"
        ;;

    5)
        echo "重置数据库..."
        cd backend_pure_python

        # 删除数据库文件
        if [ -f "paocai.db" ]; then
            rm paocai.db
            echo "删除旧数据库文件"
        fi

        python3 -m venv venv 2>/dev/null || true
        source venv/bin/activate
        pip install --upgrade pip
        pip install fastapi uvicorn sqlalchemy python-jose passlib python-multipart pydantic pydantic-settings jinja2 aiofiles bcrypt cryptography
        python init_db.py
        echo "✅ 数据库重置完成"
        ;;
        
    *)
        echo "无效选择"
        exit 1
        ;;
esac
