#!/usr/bin/env python3
"""
创建基本用户脚本
"""

from core.database import SessionLocal, engine, Base
from core.security import get_password_hash
from models.user import User, UserRole
from datetime import datetime

def create_basic_users():
    """创建基本用户"""
    
    # 创建表
    Base.metadata.create_all(bind=engine)
    
    # 创建用户
    db = SessionLocal()
    
    try:
        # 检查是否已有用户
        existing_users = db.query(User).count()
        if existing_users > 0:
            print(f'数据库中已有 {existing_users} 个用户')
            users = db.query(User).all()
            for user in users:
                print(f'- {user.username} ({user.full_name}) - {user.role.value}')
            return
        
        print('创建基本用户...')
        
        users_data = [
            {
                'username': 'admin',
                'password': 'admin123',
                'full_name': '系统管理员',
                'role': UserRole.ADMIN,
                'phone': '13800138001',
                'employee_id': 'EMP001',
                'department': '管理部',
                'position': '系统管理员',
                'is_superuser': True
            },
            {
                'username': 'manager01',
                'password': 'manager123',
                'full_name': '王经理',
                'role': UserRole.MANAGER,
                'phone': '13800138002',
                'employee_id': 'EMP002',
                'department': '餐饮部',
                'position': '餐饮经理',
                'is_superuser': False
            },
            {
                'username': 'waiter01',
                'password': 'waiter123',
                'full_name': '小李',
                'role': UserRole.WAITER,
                'phone': '13800138003',
                'employee_id': 'EMP003',
                'department': '服务部',
                'position': '服务员',
                'is_superuser': False
            },
            {
                'username': 'kitchen01',
                'password': 'kitchen123',
                'full_name': '陈师傅',
                'role': UserRole.KITCHEN_HELPER,
                'phone': '***********',
                'employee_id': 'EMP004',
                'department': '厨房部',
                'position': '厨房打荷',
                'is_superuser': False
            },
            {
                'username': 'business01',
                'password': 'business123',
                'full_name': '李商务',
                'role': UserRole.BUSINESS_CENTER,
                'phone': '***********',
                'employee_id': 'EMP005',
                'department': '商务部',
                'position': '商务中心',
                'is_superuser': False
            }
        ]
        
        for user_data in users_data:
            user = User(
                username=user_data['username'],
                password_hash=get_password_hash(user_data['password']),
                full_name=user_data['full_name'],
                role=user_data['role'],
                phone=user_data['phone'],
                employee_id=user_data['employee_id'],
                department=user_data['department'],
                position=user_data['position'],
                is_active=True,
                is_superuser=user_data['is_superuser'],
                created_at=datetime.utcnow()
            )
            db.add(user)
            print(f'✅ 创建用户: {user_data["full_name"]} ({user_data["username"]})')
        
        db.commit()
        print('\n✅ 用户创建完成')
        
        # 显示创建的用户
        users = db.query(User).all()
        print('\n📋 用户账号列表:')
        for user in users:
            # 根据用户名推断密码
            password = user.username
            if user.username == 'admin':
                password = 'admin123'
            elif user.username.startswith('manager'):
                password = 'manager123'
            elif user.username.startswith('waiter'):
                password = 'waiter123'
            elif user.username.startswith('kitchen'):
                password = 'kitchen123'
            elif user.username.startswith('business'):
                password = 'business123'
            
            print(f'   {user.role.value:12} | {user.username:10} | {password:12} | {user.full_name}')
        
    except Exception as e:
        print(f'❌ 创建用户失败: {e}')
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 开始创建基本用户...")
    create_basic_users()
    print("\n🎉 用户创建完成！")
