import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import Cookies from 'js-cookie';
import { User, LoginRequest } from '@/types';
import { authApi } from '@/api';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // Actions
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => void;
  getCurrentUser: () => Promise<void>;
  setUser: (user: User) => void;
  setToken: (token: string) => void;
  clearAuth: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      
      login: async (credentials: LoginRequest) => {
        try {
          set({ isLoading: true });
          
          const response = await authApi.login(credentials);
          const { access_token, user } = response;
          
          // 保存令牌到 Cookie
          Cookies.set('access_token', access_token, { expires: 30 });
          Cookies.set('user_info', JSON.stringify(user), { expires: 30 });
          
          set({
            user,
            token: access_token,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },
      
      logout: () => {
        // 清除 Cookie
        Cookies.remove('access_token');
        Cookies.remove('user_info');
        
        // 调用登出 API（可选，不等待结果）
        authApi.logout().catch(() => {
          // 忽略错误，因为用户可能已经离线
        });
        
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        });
        
        // 跳转到登录页
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      },
      
      getCurrentUser: async () => {
        try {
          const user = await authApi.getCurrentUser();
          set({ user });
        } catch (error) {
          // 如果获取用户信息失败，清除认证状态
          get().clearAuth();
          throw error;
        }
      },
      
      setUser: (user: User) => {
        set({ user });
        Cookies.set('user_info', JSON.stringify(user), { expires: 30 });
      },
      
      setToken: (token: string) => {
        set({ token, isAuthenticated: true });
        Cookies.set('access_token', token, { expires: 30 });
      },
      
      clearAuth: () => {
        Cookies.remove('access_token');
        Cookies.remove('user_info');
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        // 从 Cookie 中恢复认证状态
        if (typeof window !== 'undefined') {
          const token = Cookies.get('access_token');
          const userInfo = Cookies.get('user_info');
          
          if (token && userInfo) {
            try {
              const user = JSON.parse(userInfo);
              state?.setToken(token);
              state?.setUser(user);
            } catch (error) {
              // 如果解析失败，清除认证状态
              state?.clearAuth();
            }
          } else {
            state?.clearAuth();
          }
        }
      },
    }
  )
);
