{% extends "base.html" %}

{% block title %}API测试 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-bug"></i>
        API功能测试
    </h1>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-people"></i>
                    获取服务员列表测试
                </h5>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-primary" onclick="testGetWaiters()">
                    <i class="bi bi-play"></i>
                    测试获取服务员列表
                </button>
                
                <div class="mt-3">
                    <h6>测试结果：</h6>
                    <pre id="waiter-result" class="bg-light p-2" style="max-height: 300px; overflow-y: auto;">
点击按钮开始测试...
                    </pre>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person-check"></i>
                    授权功能测试
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">包厢号：</label>
                    <input type="text" class="form-control" id="test-room" value="A01" placeholder="输入包厢号">
                </div>
                
                <div class="mb-3">
                    <label class="form-label">订单ID：</label>
                    <input type="number" class="form-control" id="test-order" value="1" placeholder="输入订单ID">
                </div>
                
                <button type="button" class="btn btn-success" onclick="testAuthorizeWaiter()">
                    <i class="bi bi-play"></i>
                    测试授权服务员
                </button>
                
                <div class="mt-3">
                    <h6>测试结果：</h6>
                    <pre id="authorize-result" class="bg-light p-2" style="max-height: 300px; overflow-y: auto;">
点击按钮开始测试...
                    </pre>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i>
                    当前用户信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>用户名：</strong> {{ user.username }}
                    </div>
                    <div class="col-md-3">
                        <strong>姓名：</strong> {{ user.full_name }}
                    </div>
                    <div class="col-md-3">
                        <strong>角色：</strong> {{ user.role.value }}
                    </div>
                    <div class="col-md-3">
                        <strong>权限：</strong> 
                        {% if user.has_permission('waiter.authorize') %}
                        <span class="badge bg-success">有授权权限</span>
                        {% else %}
                        <span class="badge bg-danger">无授权权限</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function testGetWaiters() {
        const resultEl = document.getElementById('waiter-result');
        resultEl.textContent = '正在测试...';
        
        console.log('🔍 开始测试获取服务员列表');
        
        fetch('/api/users?role=waiter', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include'
        })
        .then(response => {
            console.log('🔍 响应状态:', response.status);
            console.log('🔍 响应头:', response.headers);
            
            const statusInfo = `状态码: ${response.status} ${response.statusText}\n`;
            
            if (!response.ok) {
                return response.text().then(text => {
                    throw new Error(`${statusInfo}错误详情: ${text}`);
                });
            }
            return response.json().then(data => ({
                status: statusInfo,
                data: data
            }));
        })
        .then(result => {
            console.log('✅ 成功获取数据:', result.data);
            
            let output = result.status;
            output += `数据类型: ${typeof result.data}\n`;
            output += `是否为数组: ${Array.isArray(result.data)}\n`;
            output += `数据长度: ${Array.isArray(result.data) ? result.data.length : '不是数组'}\n\n`;
            output += `原始数据:\n${JSON.stringify(result.data, null, 2)}`;
            
            resultEl.textContent = output;
        })
        .catch(error => {
            console.error('❌ 测试失败:', error);
            resultEl.textContent = `测试失败:\n${error.message}`;
        });
    }
    
    function testAuthorizeWaiter() {
        const roomNumber = document.getElementById('test-room').value;
        const orderId = document.getElementById('test-order').value;
        const resultEl = document.getElementById('authorize-result');
        
        if (!roomNumber || !orderId) {
            alert('请输入包厢号和订单ID');
            return;
        }
        
        resultEl.textContent = '正在测试授权功能...';
        
        // 这里调用实际的授权函数
        console.log('🔍 测试授权服务员:', roomNumber, orderId);
        
        // 先获取服务员列表
        fetch('/api/users?role=waiter')
        .then(response => response.json())
        .then(waiters => {
            let output = `获取到 ${waiters.length} 个服务员:\n`;
            waiters.forEach((waiter, index) => {
                output += `${index + 1}. ${waiter.full_name} (${waiter.username}) - ID: ${waiter.id}\n`;
            });
            
            if (waiters.length > 0) {
                output += `\n可以选择服务员进行授权测试`;
            } else {
                output += `\n没有可用的服务员`;
            }
            
            resultEl.textContent = output;
        })
        .catch(error => {
            resultEl.textContent = `授权测试失败:\n${error.message}`;
        });
    }
</script>
{% endblock %}
