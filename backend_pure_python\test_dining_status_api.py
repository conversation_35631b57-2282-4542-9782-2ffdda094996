#!/usr/bin/env python3
"""
测试用餐状态检查API的脚本
"""

import requests
import json

BASE_URL = "http://localhost:8001"

def test_dining_status_api():
    """测试用餐状态检查API"""
    
    # 创建会话
    session = requests.Session()
    
    print("🔍 测试用餐状态检查API...")
    
    # 1. 先登录服务员账号
    print("\n1. 登录服务员账号...")
    login_data = {
        'username': '1',
        'password': '1'
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data)
    if response.status_code == 200:
        print("✅ 服务员登录成功")
    else:
        print(f"❌ 服务员登录失败: {response.status_code}")
        return
    
    # 2. 检查认证状态
    print("\n2. 检查认证状态...")
    response = session.get(f"{BASE_URL}/api/check-auth-status")
    if response.status_code == 200:
        auth_data = response.json()
        print(f"✅ 认证状态: {json.dumps(auth_data, indent=2, ensure_ascii=False)}")
    else:
        print(f"❌ 认证状态检查失败: {response.status_code}")
    
    # 3. 测试用餐状态检查API
    print("\n3. 测试用餐状态检查API...")
    room_number = "1号镜湖厅"
    
    response = session.get(f"{BASE_URL}/waiter/check-dining-status/{room_number}")
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"✅ API返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查数据结构
            if 'success' in data and 'dining_started' in data:
                print(f"✅ 数据结构正确")
                print(f"   success: {data['success']}")
                print(f"   dining_started: {data['dining_started']}")
                
                if data.get('success') and data.get('dining_started'):
                    print("✅ 用餐已开始，前端应该允许发送指令")
                else:
                    print("⚠️ 用餐未开始，前端应该阻止发送指令")
            else:
                print("❌ 数据结构异常，缺少必要字段")
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print(f"原始响应: {response.text}")
    elif response.status_code == 401:
        print("❌ 401未授权 - 这就是问题所在！")
        print(f"响应内容: {response.text}")
    else:
        print(f"❌ API调用失败: {response.status_code}")
        print(f"响应内容: {response.text}")
    
    # 4. 测试发送指令API（验证是否真的有权限问题）
    print("\n4. 测试发送指令API...")
    command_data = {
        'room_number': room_number,
        'action_type': 'serve_dish',
        'action_content': '测试指令'
    }
    
    response = session.post(f"{BASE_URL}/waiter/send-command", json=command_data)
    print(f"发送指令状态码: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ 发送指令成功 - 说明权限正常")
    else:
        print(f"❌ 发送指令失败: {response.status_code}")
        print(f"响应内容: {response.text}")

if __name__ == "__main__":
    test_dining_status_api()
