{% extends "base.html" %}

{% block title %}菜单管理 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-book"></i>
        菜单管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i>
                刷新
            </button>
            {% if user.has_permission('menu.manage') %}
            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addDishModal">
                <i class="bi bi-plus"></i>
                新增菜品
            </button>
            {% endif %}
        </div>
    </div>
</div>

<!-- 菜品分类统计 -->
<div class="row mb-4">
    {% set categories = {} %}
    {% for dish in dishes %}
        {% if categories.update({dish.category.value: categories.get(dish.category.value, 0) + 1}) %}{% endif %}
    {% endfor %}
    
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ dishes|length }}</h5>
                <p class="card-text">总菜品</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">{{ dishes|selectattr("status.value", "equalto", "available")|list|length }}</h5>
                <p class="card-text">可供应</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ dishes|selectattr("is_signature", "equalto", true)|list|length }}</h5>
                <p class="card-text">招牌菜</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">{{ dishes|selectattr("is_recommended", "equalto", true)|list|length }}</h5>
                <p class="card-text">推荐菜</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-danger">{{ dishes|selectattr("is_new", "equalto", true)|list|length }}</h5>
                <p class="card-text">新品</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-secondary">{{ dishes|selectattr("is_popular", "equalto", true)|list|length }}</h5>
                <p class="card-text">热门</p>
            </div>
        </div>
    </div>
</div>

<!-- 筛选器 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">搜索菜品</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="菜品名称、编码" value="{{ request.query_params.get('search', '') }}">
            </div>
            <div class="col-md-2">
                <label for="category" class="form-label">分类</label>
                <select class="form-select" id="category" name="category">
                    <option value="">全部分类</option>
                    <option value="cold_dish" {% if request.query_params.get('category') == 'cold_dish' %}selected{% endif %}>凉菜</option>
                    <option value="hot_dish" {% if request.query_params.get('category') == 'hot_dish' %}selected{% endif %}>热菜</option>
                    <option value="soup" {% if request.query_params.get('category') == 'soup' %}selected{% endif %}>汤羹</option>
                    <option value="staple" {% if request.query_params.get('category') == 'staple' %}selected{% endif %}>主食</option>
                    <option value="dessert" {% if request.query_params.get('category') == 'dessert' %}selected{% endif %}>甜品</option>
                    <option value="beverage" {% if request.query_params.get('category') == 'beverage' %}selected{% endif %}>饮品</option>
                    <option value="seafood" {% if request.query_params.get('category') == 'seafood' %}selected{% endif %}>海鲜</option>
                    <option value="specialty" {% if request.query_params.get('category') == 'specialty' %}selected{% endif %}>招牌菜</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">状态</label>
                <select class="form-select" id="status" name="status">
                    <option value="">全部状态</option>
                    <option value="available" {% if request.query_params.get('status') == 'available' %}selected{% endif %}>可供应</option>
                    <option value="unavailable" {% if request.query_params.get('status') == 'unavailable' %}selected{% endif %}>暂不供应</option>
                    <option value="seasonal" {% if request.query_params.get('status') == 'seasonal' %}selected{% endif %}>时令菜</option>
                    <option value="limited" {% if request.query_params.get('status') == 'limited' %}selected{% endif %}>限量供应</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="spicy" class="form-label">辣度</label>
                <select class="form-select" id="spicy" name="spicy">
                    <option value="">全部辣度</option>
                    <option value="none" {% if request.query_params.get('spicy') == 'none' %}selected{% endif %}>不辣</option>
                    <option value="mild" {% if request.query_params.get('spicy') == 'mild' %}selected{% endif %}>微辣</option>
                    <option value="medium" {% if request.query_params.get('spicy') == 'medium' %}selected{% endif %}>中辣</option>
                    <option value="hot" {% if request.query_params.get('spicy') == 'hot' %}selected{% endif %}>辣</option>
                    <option value="extra_hot" {% if request.query_params.get('spicy') == 'extra_hot' %}selected{% endif %}>特辣</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i>
                        搜索
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 菜品列表 -->
<div class="card shadow">
    <div class="card-header">
        <h5 class="mb-0">菜品列表</h5>
    </div>
    <div class="card-body">
        {% if dishes %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>图片</th>
                        <th>菜品信息</th>
                        <th>分类</th>
                        <th>价格</th>
                        <th>状态</th>
                        <th>口味</th>
                        <th>制作时间</th>
                        <th>销量</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for dish in dishes %}
                    <tr>
                        <td>
                            {% if dish.image_url %}
                            <img src="{{ dish.image_url }}" alt="{{ dish.name }}" 
                                 class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                            {% else %}
                            <div class="bg-light d-flex align-items-center justify-content-center" 
                                 style="width: 60px; height: 60px; border-radius: 0.375rem;">
                                <i class="bi bi-image text-muted"></i>
                            </div>
                            {% endif %}
                        </td>
                        <td>
                            <div>
                                <strong>{{ dish.name }}</strong>
                                {% if dish.is_signature %}
                                <span class="badge bg-warning"><i class="bi bi-star"></i> 招牌</span>
                                {% endif %}
                                {% if dish.is_recommended %}
                                <span class="badge bg-primary">推荐</span>
                                {% endif %}
                                {% if dish.is_new %}
                                <span class="badge bg-success"><i class="bi bi-gift"></i> 新品</span>
                                {% endif %}
                                {% if dish.is_popular %}
                                <span class="badge bg-danger"><i class="bi bi-fire"></i> 热门</span>
                                {% endif %}
                            </div>
                            {% if dish.english_name %}
                            <small class="text-muted">{{ dish.english_name }}</small><br>
                            {% endif %}
                            {% if dish.code %}
                            <small class="text-muted">编码: {{ dish.code }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="dish-category">
                                {% if dish.category.value == 'cold_dish' %}凉菜
                                {% elif dish.category.value == 'hot_dish' %}热菜
                                {% elif dish.category.value == 'soup' %}汤羹
                                {% elif dish.category.value == 'staple' %}主食
                                {% elif dish.category.value == 'dessert' %}甜品
                                {% elif dish.category.value == 'beverage' %}饮品
                                {% elif dish.category.value == 'seafood' %}海鲜
                                {% elif dish.category.value == 'specialty' %}招牌菜
                                {% else %}{{ dish.category.value }}
                                {% endif %}
                            </span>
                        </td>
                        <td>
                            <div class="fw-bold text-primary">¥{{ "%.2f"|format(dish.price) }}</div>
                            {% if dish.member_price and dish.member_price != dish.price %}
                            <small class="text-warning">会员价: ¥{{ "%.2f"|format(dish.member_price) }}</small><br>
                            {% endif %}
                            {% if dish.cost %}
                            <small class="text-muted">成本: ¥{{ "%.2f"|format(dish.cost) }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{% if dish.status.value == 'available' %}success{% elif dish.status.value == 'unavailable' %}secondary{% elif dish.status.value == 'seasonal' %}warning{% elif dish.status.value == 'limited' %}info{% else %}danger{% endif %}">
                                {% if dish.status.value == 'available' %}可供应
                                {% elif dish.status.value == 'unavailable' %}暂不供应
                                {% elif dish.status.value == 'seasonal' %}时令菜
                                {% elif dish.status.value == 'limited' %}限量供应
                                {% elif dish.status.value == 'discontinued' %}已停售
                                {% else %}{{ dish.status.value }}
                                {% endif %}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-{% if dish.spicy_level.value == 'none' %}secondary{% else %}danger{% endif %}">
                                {% if dish.spicy_level.value == 'none' %}不辣
                                {% elif dish.spicy_level.value == 'mild' %}微辣
                                {% elif dish.spicy_level.value == 'medium' %}中辣
                                {% elif dish.spicy_level.value == 'hot' %}辣
                                {% elif dish.spicy_level.value == 'extra_hot' %}特辣
                                {% else %}{{ dish.spicy_level.value }}
                                {% endif %}
                            </span>
                            {% if dish.cooking_method %}
                            <br><small class="text-muted">
                                {% if dish.cooking_method.value == 'stir_fry' %}炒
                                {% elif dish.cooking_method.value == 'steam' %}蒸
                                {% elif dish.cooking_method.value == 'boil' %}煮
                                {% elif dish.cooking_method.value == 'braise' %}红烧
                                {% elif dish.cooking_method.value == 'grill' %}烤
                                {% elif dish.cooking_method.value == 'deep_fry' %}炸
                                {% elif dish.cooking_method.value == 'cold_mix' %}凉拌
                                {% elif dish.cooking_method.value == 'stew' %}炖
                                {% else %}{{ dish.cooking_method.value }}
                                {% endif %}
                            </small>
                            {% endif %}
                        </td>
                        <td>
                            {% if dish.total_time > 0 %}
                            {{ dish.total_time }}分钟
                            {% else %}
                            -
                            {% endif %}
                        </td>
                        <td>
                            <div class="text-center">
                                <div class="fw-bold">{{ dish.sales_count }}</div>
                                <small class="text-muted">份</small>
                            </div>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-info btn-sm" title="查看详情" 
                                        data-bs-toggle="modal" data-bs-target="#viewModal{{ dish.id }}">
                                    <i class="bi bi-eye"></i>
                                </button>
                                
                                {% if user.has_permission('menu.manage') %}
                                <button type="button" class="btn btn-warning btn-sm" title="编辑" 
                                        data-bs-toggle="modal" data-bs-target="#editModal{{ dish.id }}">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                
                                <button type="button" class="btn btn-danger btn-sm" title="删除"
                                        onclick="deleteDish({{ dish.id }}, '{{ dish.name }}')">
                                    <i class="bi bi-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-book text-muted" style="font-size: 4rem;"></i>
            <h4 class="text-muted mt-3">暂无菜品</h4>
            <p class="text-muted">点击上方"新增菜品"按钮添加第一个菜品</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 菜品详情模态框 -->
{% for dish in dishes %}
<div class="modal fade" id="viewModal{{ dish.id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ dish.name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        {% if dish.image_url %}
                        <img src="{{ dish.image_url }}" alt="{{ dish.name }}" class="img-fluid rounded">
                        {% else %}
                        <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 200px;">
                            <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-md-8">
                        <h5>{{ dish.name }}</h5>
                        {% if dish.english_name %}
                        <p class="text-muted">{{ dish.english_name }}</p>
                        {% endif %}
                        
                        {% if dish.description %}
                        <p>{{ dish.description }}</p>
                        {% endif %}
                        
                        <div class="row">
                            <div class="col-6">
                                <strong>价格:</strong> ¥{{ "%.2f"|format(dish.price) }}
                            </div>
                            <div class="col-6">
                                <strong>单位:</strong> {{ dish.unit }}
                            </div>
                        </div>
                        
                        {% if dish.ingredients %}
                        <div class="mt-2">
                            <strong>主要食材:</strong> {{ dish.ingredients }}
                        </div>
                        {% endif %}
                        
                        {% if dish.allergens %}
                        <div class="mt-2">
                            <strong>过敏原:</strong> {{ dish.allergens }}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endfor %}

{% endblock %}

{% block extra_js %}
<script>
    function deleteDish(dishId, dishName) {
        if (confirm('确定要删除菜品 "' + dishName + '" 吗？')) {
            fetch('/menu/' + dishId + '/delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            }).then(response => {
                if (response.ok) {
                    location.reload();
                } else {
                    alert('删除失败');
                }
            });
        }
    }
</script>
{% endblock %}
