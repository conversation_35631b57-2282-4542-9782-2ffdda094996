import dayjs from 'dayjs';
import { CURRENCY_SYMBOL, DATETIME_FORMAT, DATE_FORMAT, TIME_FORMAT } from './constants';

/**
 * 格式化金额
 * @param amount 金额
 * @param showSymbol 是否显示货币符号
 * @returns 格式化后的金额字符串
 */
export const formatCurrency = (amount: number | string, showSymbol = true): string => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(num)) return '0.00';
  
  const formatted = num.toFixed(2);
  return showSymbol ? `${CURRENCY_SYMBOL}${formatted}` : formatted;
};

/**
 * 格式化日期时间
 * @param date 日期
 * @param format 格式
 * @returns 格式化后的日期字符串
 */
export const formatDateTime = (date: string | Date, format = DATETIME_FORMAT): string => {
  if (!date) return '';
  return dayjs(date).format(format);
};

/**
 * 格式化日期
 * @param date 日期
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: string | Date): string => {
  return formatDateTime(date, DATE_FORMAT);
};

/**
 * 格式化时间
 * @param date 日期
 * @returns 格式化后的时间字符串
 */
export const formatTime = (date: string | Date): string => {
  return formatDateTime(date, TIME_FORMAT);
};

/**
 * 计算时间差（分钟）
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @returns 时间差（分钟）
 */
export const getTimeDiffInMinutes = (startTime: string | Date, endTime: string | Date = new Date()): number => {
  const start = dayjs(startTime);
  const end = dayjs(endTime);
  return end.diff(start, 'minute');
};

/**
 * 格式化时长
 * @param minutes 分钟数
 * @returns 格式化后的时长字符串
 */
export const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes}分钟`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours}小时`;
  }
  
  return `${hours}小时${remainingMinutes}分钟`;
};

/**
 * 生成随机ID
 * @param length 长度
 * @returns 随机ID
 */
export const generateId = (length = 8): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间
 * @returns 防抖后的函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 限制时间
 * @returns 节流后的函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 * @returns 拷贝后的对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T;
  if (typeof obj === 'object') {
    const clonedObj = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
  return obj;
};

/**
 * 检查是否为空值
 * @param value 要检查的值
 * @returns 是否为空
 */
export const isEmpty = (value: any): boolean => {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string') return value.trim() === '';
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
};

/**
 * 获取文件扩展名
 * @param filename 文件名
 * @returns 扩展名
 */
export const getFileExtension = (filename: string): string => {
  return filename.slice(((filename.lastIndexOf('.') - 1) >>> 0) + 2);
};

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 检查权限
 * @param userRole 用户角色
 * @param requiredPermissions 需要的权限
 * @returns 是否有权限
 */
export const hasPermission = (userRole: string, requiredPermissions: string[]): boolean => {
  // 管理员拥有所有权限
  if (userRole === 'admin') return true;
  
  // 这里可以根据实际需求实现更复杂的权限检查逻辑
  return requiredPermissions.some(permission => {
    // 简化的权限检查，实际应用中应该有更完善的权限系统
    return true;
  });
};

/**
 * 获取相对时间
 * @param date 日期
 * @returns 相对时间字符串
 */
export const getRelativeTime = (date: string | Date): string => {
  const now = dayjs();
  const target = dayjs(date);
  const diffInMinutes = now.diff(target, 'minute');
  
  if (diffInMinutes < 1) return '刚刚';
  if (diffInMinutes < 60) return `${diffInMinutes}分钟前`;
  
  const diffInHours = now.diff(target, 'hour');
  if (diffInHours < 24) return `${diffInHours}小时前`;
  
  const diffInDays = now.diff(target, 'day');
  if (diffInDays < 7) return `${diffInDays}天前`;
  
  return formatDate(date);
};

/**
 * 计算利润率
 * @param price 售价
 * @param cost 成本
 * @returns 利润率百分比
 */
export const calculateProfitMargin = (price: number, cost: number): number => {
  if (cost === 0) return 0;
  return Math.round(((price - cost) / price) * 100);
};

/**
 * 生成颜色
 * @param str 字符串
 * @returns 颜色值
 */
export const stringToColor = (str: string): string => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  const hue = hash % 360;
  return `hsl(${hue}, 70%, 50%)`;
};

/**
 * 验证手机号
 * @param phone 手机号
 * @returns 是否有效
 */
export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

/**
 * 验证邮箱
 * @param email 邮箱
 * @returns 是否有效
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};
