from sqlalchemy import Column, Integer, String, Boolean, DateTime, Enum, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from core.database import Base


class UserRole(str, enum.Enum):
    """用户角色枚举"""
    ADMIN = "admin"                    # 系统管理员
    MANAGER = "manager"                # 餐饮经理
    CHEF_MANAGER = "chef_manager"      # 厨师长
    WAITER = "waiter"                  # 服务员
    KITCHEN_HELPER = "kitchen_helper"  # 厨房打荷
    BUSINESS_CENTER = "business_center" # 商务中心


class UserStatus(str, enum.Enum):
    """用户状态枚举"""
    ACTIVE = "active"      # 激活
    INACTIVE = "inactive"  # 停用
    SUSPENDED = "suspended"  # 暂停


class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False, comment="用户名")
    email = Column(String(100), unique=True, index=True, nullable=True, comment="邮箱")
    phone = Column(String(20), unique=True, index=True, nullable=True, comment="手机号")
    hashed_password = Column(String(255), nullable=False, comment="密码哈希")
    
    # 基本信息
    full_name = Column(String(100), nullable=False, comment="姓名")
    role = Column(Enum(UserRole), nullable=False, default=UserRole.WAITER, comment="角色")
    status = Column(Enum(UserStatus), nullable=False, default=UserStatus.ACTIVE, comment="状态")
    
    # 工作信息
    employee_id = Column(String(20), unique=True, index=True, nullable=True, comment="员工编号")
    department = Column(String(50), nullable=True, comment="部门")
    position = Column(String(50), nullable=True, comment="职位")
    
    # 权限和设置
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_superuser = Column(Boolean, default=False, comment="是否超级用户")
    avatar_url = Column(String(255), nullable=True, comment="头像URL")
    
    # 工作区域（服务员负责的包厢/区域）
    assigned_areas = Column(Text, nullable=True, comment="分配的工作区域（JSON格式）")
    assigned_tables = Column(Text, nullable=True, comment="分配的餐桌ID列表，逗号分隔")
    authorized_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="授权人ID")
    is_authorized = Column(Boolean, default=False, comment="是否已授权")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    last_login_at = Column(DateTime(timezone=True), nullable=True, comment="最后登录时间")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"
    
    @property
    def is_management(self) -> bool:
        """是否为管理层"""
        return self.role in [UserRole.ADMIN, UserRole.MANAGER]

    @property
    def is_kitchen_staff(self) -> bool:
        """是否为厨房工作人员"""
        return self.role in [UserRole.CHEF_MANAGER, UserRole.KITCHEN_HELPER]

    @property
    def is_service_staff(self) -> bool:
        """是否为服务人员"""
        return self.role in [UserRole.WAITER]
    
    def has_permission(self, permission: str) -> bool:
        """检查用户是否有特定权限"""
        # 超级用户拥有所有权限
        if self.is_superuser:
            return True
            
        # 根据角色定义权限
        role_permissions = {
            UserRole.ADMIN: ["*"],  # 所有权限
            UserRole.MANAGER: [
                "dashboard.view", "user.manage", "waiter.authorize", "waiter.change",
                "table.view", "table.manage", "order.view", "kitchen.view"
            ],
            UserRole.CHEF_MANAGER: [
                "dashboard.view", "kitchen.display", "kitchen.manage", "dish.mark_done",
                "kitchen.voice", "order.view", "order.manage", "order.edit_after_start",
                "dish.cook", "dish.prepare", "dish.serve", "kitchen.view"
            ],
            UserRole.WAITER: [
                "waiter.view_menu", "waiter.confirm_dish", "waiter.serve",
                "waiter.rush", "waiter.add_staple"
            ],
            UserRole.KITCHEN_HELPER: [
                "dashboard.view", "kitchen.display", "kitchen.manage", "dish.mark_done",
                "kitchen.voice", "order.view", "dish.cook", "dish.prepare", "dish.serve",
                "kitchen.view"
            ],
            UserRole.BUSINESS_CENTER: [
                "dashboard.view", "order.create", "order.view", "order.create_from_menu"
            ]
        }
        
        permissions = role_permissions.get(self.role, [])
        return "*" in permissions or permission in permissions

    # 关系（需要在所有模型定义后导入）
    # orders = relationship("Order", back_populates="waiter")
    # sent_actions = relationship("WaiterAction", foreign_keys="WaiterAction.waiter_id", back_populates="waiter")
    # processed_actions = relationship("WaiterAction", foreign_keys="WaiterAction.processed_by", back_populates="processor")
