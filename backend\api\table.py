from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_
from pydantic import BaseModel
from decimal import Decimal

from core.database import get_db
from models.table import Table, TableType, TableStatus
from models.user import User
from .auth import get_current_user, require_permission

router = APIRouter()


# Pydantic 模型
class TableCreate(BaseModel):
    number: str
    name: Optional[str] = None
    table_type: TableType
    capacity: int
    min_capacity: Optional[int] = None
    floor: Optional[str] = None
    area: Optional[str] = None
    location_description: Optional[str] = None
    has_tv: bool = False
    has_karaoke: bool = False
    has_mahjong: bool = False
    has_projector: bool = False
    has_wifi: bool = True
    has_air_conditioning: bool = True
    minimum_charge: Optional[Decimal] = None
    service_charge_rate: Optional[Decimal] = None
    hourly_rate: Optional[Decimal] = None
    is_vip_only: bool = False
    requires_reservation: bool = False
    notes: Optional[str] = None


class TableUpdate(BaseModel):
    name: Optional[str] = None
    table_type: Optional[TableType] = None
    capacity: Optional[int] = None
    min_capacity: Optional[int] = None
    floor: Optional[str] = None
    area: Optional[str] = None
    location_description: Optional[str] = None
    has_tv: Optional[bool] = None
    has_karaoke: Optional[bool] = None
    has_mahjong: Optional[bool] = None
    has_projector: Optional[bool] = None
    has_wifi: Optional[bool] = None
    has_air_conditioning: Optional[bool] = None
    minimum_charge: Optional[Decimal] = None
    service_charge_rate: Optional[Decimal] = None
    hourly_rate: Optional[Decimal] = None
    is_vip_only: Optional[bool] = None
    requires_reservation: Optional[bool] = None
    is_active: Optional[bool] = None
    notes: Optional[str] = None


class TableStatusUpdate(BaseModel):
    status: TableStatus
    current_guests: Optional[int] = None
    estimated_duration: Optional[int] = None
    assigned_waiter_id: Optional[int] = None
    special_requirements: Optional[str] = None


class TableResponse(BaseModel):
    id: int
    number: str
    name: Optional[str] = None
    table_type: TableType
    status: TableStatus
    capacity: int
    min_capacity: Optional[int] = None
    floor: Optional[str] = None
    area: Optional[str] = None
    location_description: Optional[str] = None
    facilities_list: List[str]
    minimum_charge: Optional[Decimal] = None
    service_charge_rate: Optional[Decimal] = None
    hourly_rate: Optional[Decimal] = None
    is_vip_only: bool
    requires_reservation: bool
    current_guests: int
    occupied_since: Optional[str] = None
    estimated_duration: Optional[int] = None
    assigned_waiter_id: Optional[int] = None
    is_active: bool
    notes: Optional[str] = None
    created_at: str

    class Config:
        from_attributes = True


@router.get("/", response_model=List[TableResponse])
async def get_tables(
    table_type: Optional[TableType] = Query(None),
    status: Optional[TableStatus] = Query(None),
    floor: Optional[str] = Query(None),
    area: Optional[str] = Query(None),
    available_only: bool = Query(False),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取餐桌列表"""
    query = db.query(Table)
    
    # 类型过滤
    if table_type:
        query = query.filter(Table.table_type == table_type)
    
    # 状态过滤
    if status:
        query = query.filter(Table.status == status)
    elif available_only:
        query = query.filter(Table.status == TableStatus.AVAILABLE)
    
    # 楼层过滤
    if floor:
        query = query.filter(Table.floor == floor)
    
    # 区域过滤
    if area:
        query = query.filter(Table.area == area)
    
    # 只显示启用的餐桌
    query = query.filter(Table.is_active == True)
    
    # 排序
    query = query.order_by(Table.number)
    
    tables = query.all()
    return [TableResponse.from_orm(table) for table in tables]


@router.post("/", response_model=TableResponse)
async def create_table(
    table_data: TableCreate,
    current_user: User = Depends(require_permission("table.manage")),
    db: Session = Depends(get_db)
):
    """创建餐桌"""
    # 检查桌号是否已存在
    if db.query(Table).filter(Table.number == table_data.number).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="桌号已存在"
        )
    
    table = Table(**table_data.dict())
    db.add(table)
    db.commit()
    db.refresh(table)
    
    return TableResponse.from_orm(table)


@router.get("/{table_id}", response_model=TableResponse)
async def get_table(
    table_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取餐桌详情"""
    table = db.query(Table).filter(Table.id == table_id).first()
    if not table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="餐桌不存在"
        )
    
    return TableResponse.from_orm(table)


@router.put("/{table_id}", response_model=TableResponse)
async def update_table(
    table_id: int,
    table_data: TableUpdate,
    current_user: User = Depends(require_permission("table.manage")),
    db: Session = Depends(get_db)
):
    """更新餐桌信息"""
    table = db.query(Table).filter(Table.id == table_id).first()
    if not table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="餐桌不存在"
        )
    
    # 更新餐桌信息
    update_data = table_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(table, field, value)
    
    db.commit()
    db.refresh(table)
    
    return TableResponse.from_orm(table)


@router.patch("/{table_id}/status", response_model=TableResponse)
async def update_table_status(
    table_id: int,
    status_data: TableStatusUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新餐桌状态"""
    table = db.query(Table).filter(Table.id == table_id).first()
    if not table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="餐桌不存在"
        )
    
    # 检查权限
    if not current_user.has_permission("table.manage"):
        # 服务员只能更新自己负责的餐桌
        if current_user.role.value == "waiter" and table.assigned_waiter_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只能操作自己负责的餐桌"
            )
    
    # 更新状态
    table.status = status_data.status
    
    if status_data.current_guests is not None:
        table.current_guests = status_data.current_guests
    
    if status_data.estimated_duration is not None:
        table.estimated_duration = status_data.estimated_duration
    
    if status_data.assigned_waiter_id is not None:
        table.assigned_waiter_id = status_data.assigned_waiter_id
    
    if status_data.special_requirements is not None:
        table.special_requirements = status_data.special_requirements
    
    # 设置占用时间
    if status_data.status == TableStatus.OCCUPIED and not table.occupied_since:
        from datetime import datetime
        table.occupied_since = datetime.utcnow()
    elif status_data.status == TableStatus.AVAILABLE:
        table.occupied_since = None
        table.current_guests = 0
        table.estimated_duration = None
        table.special_requirements = None
    
    db.commit()
    db.refresh(table)
    
    return TableResponse.from_orm(table)


@router.delete("/{table_id}")
async def delete_table(
    table_id: int,
    current_user: User = Depends(require_permission("table.manage")),
    db: Session = Depends(get_db)
):
    """删除餐桌"""
    table = db.query(Table).filter(Table.id == table_id).first()
    if not table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="餐桌不存在"
        )
    
    # 检查是否有未完成的订单
    from models.order import Order, OrderStatus
    active_orders = db.query(Order).filter(
        Order.table_id == table_id,
        Order.status.in_([
            OrderStatus.CONFIRMED,
            OrderStatus.PENDING_START,
            OrderStatus.IN_PROGRESS
        ])
    ).first()
    
    if active_orders:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该餐桌有未完成的订单，无法删除"
        )
    
    # 软删除：设置为非激活状态
    table.is_active = False
    db.commit()
    
    return {"message": "餐桌已删除"}


@router.get("/stats/overview")
async def get_table_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取餐桌统计概览"""
    total_tables = db.query(Table).filter(Table.is_active == True).count()
    available_tables = db.query(Table).filter(
        Table.is_active == True,
        Table.status == TableStatus.AVAILABLE
    ).count()
    occupied_tables = db.query(Table).filter(
        Table.is_active == True,
        Table.status == TableStatus.OCCUPIED
    ).count()
    reserved_tables = db.query(Table).filter(
        Table.is_active == True,
        Table.status == TableStatus.RESERVED
    ).count()
    
    # 按类型统计
    type_stats = {}
    for table_type in TableType:
        count = db.query(Table).filter(
            Table.is_active == True,
            Table.table_type == table_type
        ).count()
        type_stats[table_type.value] = count
    
    return {
        "total_tables": total_tables,
        "available_tables": available_tables,
        "occupied_tables": occupied_tables,
        "reserved_tables": reserved_tables,
        "occupancy_rate": round((occupied_tables + reserved_tables) / total_tables * 100, 2) if total_tables > 0 else 0,
        "type_stats": type_stats
    }
