# 🚀 部署运行指南

## 📋 方式一：一键启动（推荐）

### Windows 用户
1. 双击运行 `start.bat`
2. 选择启动模式：
   - 选择 `1` - 完整启动（后端+前端）
   - 选择 `5` - 首次运行请先安装依赖

### Linux/Mac 用户
```bash
chmod +x start.sh
./start.sh
```

## 📋 方式二：手动部署

### 1. 安装 Python 环境
```bash
# 检查 Python 版本
python3 --version  # 需要 3.9+

# 如果没有安装，请访问 https://python.org 下载安装
```

### 2. 安装 Node.js 环境
```bash
# 检查 Node.js 版本
node --version  # 需要 18.0+

# 如果没有安装，请访问 https://nodejs.org 下载安装
```

### 3. 安装 PostgreSQL 数据库
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# CentOS/RHEL
sudo yum install postgresql-server postgresql-contrib

# macOS (使用 Homebrew)
brew install postgresql

# Windows
# 下载安装包：https://www.postgresql.org/download/windows/
```

### 4. 配置数据库
```bash
# 启动 PostgreSQL 服务
sudo systemctl start postgresql  # Linux
brew services start postgresql   # macOS

# 创建数据库和用户
sudo -u postgres psql
CREATE DATABASE paocai_db;
CREATE USER paocai_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE paocai_db TO paocai_user;
\q
```

### 5. 配置后端
```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
# Linux/Mac:
source venv/bin/activate
# Windows:
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 配置数据库连接（可选）
# 编辑 core/config.py 中的 DATABASE_URL
```

### 6. 初始化数据库
```bash
# 在 backend 目录下
python init_db.py
```

### 7. 启动后端服务
```bash
# 在 backend 目录下
uvicorn main:socket_app --reload --host 0.0.0.0 --port 8000
```

### 8. 配置前端
```bash
# 新开一个终端，进入前端目录
cd frontend

# 安装依赖
npm install

# 启动前端服务
npm run dev
```

## 📋 方式三：Docker 部署

### 1. 安装 Docker
```bash
# 安装 Docker 和 Docker Compose
# 访问 https://docs.docker.com/get-docker/ 获取安装指南
```

### 2. 使用 Docker Compose 启动
```bash
# 在项目根目录下
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 3. 初始化数据库（Docker 方式）
```bash
# 进入后端容器
docker-compose exec backend python init_db.py
```

## 🌐 访问系统

启动成功后，可以通过以下地址访问：

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 🔑 默认登录账号

| 角色 | 用户名 | 密码 | 说明 |
|------|--------|------|------|
| 系统管理员 | admin | admin123 | 拥有所有权限 |
| 餐饮经理 | manager01 | manager123 | 管理端功能 |
| 服务员 | waiter01 | waiter123 | 服务员端功能 |
| 厨师长 | chef01 | chef123 | 厨房端功能 |
| 打荷员 | kitchen01 | kitchen123 | 厨房辅助功能 |

## 🧪 验证系统

运行测试脚本验证系统是否正常工作：

```bash
python test_system.py
```

## ⚠️ 常见问题

### 1. 端口被占用
```bash
# 检查端口占用
netstat -tulpn | grep :8000  # Linux/Mac
netstat -ano | findstr :8000  # Windows

# 修改端口（可选）
# 后端：修改启动命令中的 --port 参数
# 前端：修改 package.json 中的 dev 脚本
```

### 2. 数据库连接失败
```bash
# 检查 PostgreSQL 服务状态
sudo systemctl status postgresql  # Linux
brew services list | grep postgresql  # macOS

# 检查数据库配置
# 编辑 backend/core/config.py 中的 DATABASE_URL
```

### 3. Python 依赖安装失败
```bash
# 升级 pip
pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 4. Node.js 依赖安装失败
```bash
# 清除缓存
npm cache clean --force

# 使用国内镜像源
npm install --registry https://registry.npmmirror.com
```

### 5. 权限问题（Linux/Mac）
```bash
# 给脚本执行权限
chmod +x start.sh
chmod +x test_system.py

# 如果遇到端口权限问题，可以使用其他端口
# 或者使用 sudo（不推荐）
```

## 🔧 生产环境部署

### 1. 环境变量配置
```bash
# 创建 .env 文件
cp backend/.env.example backend/.env

# 编辑配置
DATABASE_URL=postgresql://user:password@localhost:5432/paocai_db
SECRET_KEY=your-secret-key-here
DEBUG=False
```

### 2. 使用 Nginx 反向代理
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # 后端 API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # WebSocket
    location /socket.io/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

### 3. 使用 PM2 管理进程
```bash
# 安装 PM2
npm install -g pm2

# 启动后端
pm2 start "uvicorn main:socket_app --host 0.0.0.0 --port 8000" --name paocai-backend

# 启动前端
pm2 start "npm run start" --name paocai-frontend

# 保存配置
pm2 save
pm2 startup
```

## 📞 技术支持

如果遇到问题，请：

1. 检查系统日志和错误信息
2. 确认所有依赖都已正确安装
3. 验证数据库连接配置
4. 运行测试脚本检查系统状态

---

**祝您使用愉快！** 🎉
