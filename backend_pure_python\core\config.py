from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # 应用配置
    APP_NAME: str = "暨阳湖大酒店传菜管理系统"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./paocai.db"
    # 如果使用 PostgreSQL，取消注释下面的行并注释上面的行
    # DATABASE_URL: str = "postgresql://postgres:password@localhost:5432/paocai_db"
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30 * 24 * 60  # 30天
    
    # 文件上传配置
    UPLOAD_DIR: str = "static/uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    
    # Redis 配置（可选）
    REDIS_URL: Optional[str] = None
    
    class Config:
        env_file = ".env"


settings = Settings()
